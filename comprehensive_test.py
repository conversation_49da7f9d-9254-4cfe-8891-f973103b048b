#!/usr/bin/env python3
"""
Comprehensive test of ************** server with different approaches
"""
import subprocess
import socket
import os

def test_server_connectivity():
    server = "**************"
    
    print("=== Testing Server Connectivity ===")
    
    # 1. Ping test
    print(f"\n1. Pinging {server}...")
    try:
        result = subprocess.run(['ping', '-c', '3', server], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Server is reachable")
        else:
            print("❌ Server not reachable")
            return
    except Exception as e:
        print(f"❌ Ping failed: {e}")
        return
    
    # 2. SMB port check
    print(f"\n2. Checking SMB port (445)...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((server, 445))
        sock.close()
        if result == 0:
            print("✅ SMB port 445 is open")
        else:
            print("❌ SMB port 445 is not accessible")
            return
    except Exception as e:
        print(f"❌ Port check failed: {e}")
        return
    
    # 3. Try anonymous share listing
    print(f"\n3. Anonymous share listing...")
    try:
        result = subprocess.run([
            'smbclient', '-L', server, '-N'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Anonymous share listing successful")
            print("Available shares:")
            lines = result.stdout.splitlines()
            started = False
            for line in lines:
                if "Sharename" in line and "Type" in line:
                    started = True
                    continue
                if started and line.strip() and not line.startswith("-"):
                    if "Disk" in line:
                        share_name = line.split()[0]
                        print(f"  - {share_name}")
        else:
            print(f"❌ Anonymous share listing failed")
            print(f"Error: {result.stderr}")
    except Exception as e:
        print(f"❌ Share listing error: {e}")
    
    # 4. Try nmblookup to get hostname
    print(f"\n4. NetBIOS name lookup...")
    try:
        result = subprocess.run(['nmblookup', '-A', server], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ NetBIOS lookup successful")
            print(result.stdout)
        else:
            print("❌ NetBIOS lookup failed")
    except FileNotFoundError:
        print("⚠️ nmblookup not installed")
    except Exception as e:
        print(f"❌ NetBIOS lookup error: {e}")

def test_specific_workgroup_auth():
    server = "**************"
    
    print("\n=== Testing Workgroup Authentication ===")
    
    # Test with different workgroup/domain scenarios
    auth_scenarios = [
        ("WORKGROUP", "Michael Nichols", "jsthogn"),
        ("WORKGROUP", "michaelnichols", "jsthogn"),
        ("", "Michael Nichols", "jsthogn"),  # Empty workgroup
        ("HOME", "Michael Nichols", "jsthogn"),  # Try HOME workgroup
    ]
    
    for workgroup, username, password in auth_scenarios:
        print(f"\n--- Testing: workgroup='{workgroup}', user='{username}' ---")
        try:
            if workgroup:
                auth_string = f"{workgroup}\\{username}%{password}"
            else:
                auth_string = f"{username}%{password}"
                
            result = subprocess.run([
                'smbclient', '-L', server, '-U', auth_string
            ], capture_output=True, text=True, timeout=8)
            
            if result.returncode == 0:
                print(f"✅ SUCCESS! Workgroup: {workgroup}, User: {username}")
                return workgroup, username, password
            else:
                print(f"❌ Failed")
                if result.stderr and "NT_STATUS_LOGON_FAILURE" not in result.stderr:
                    print(f"Error: {result.stderr.strip()}")
                    
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    return None, None, None

if __name__ == "__main__":
    test_server_connectivity()
    workgroup, username, password = test_specific_workgroup_auth()
    
    if workgroup:
        print(f"\n✅ FOUND WORKING CONFIGURATION:")
        print(f"   Workgroup: {workgroup}")
        print(f"   Username: {username}")
        print(f"   Password: {password}")
        
        # Update the main file with correct credentials
        print(f"\n💡 Suggested update for lan-file-transfer.py:")
        print(f"   theater_username = \"{username}\"")
        print(f"   theater_password = \"{password}\"")
        print(f"   theater_workgroup = \"{workgroup}\"")
    else:
        print(f"\n❌ No working authentication found.")