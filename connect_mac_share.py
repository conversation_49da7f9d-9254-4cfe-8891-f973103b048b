#!/usr/bin/env python3

import subprocess
import os
import sys
import time
import tempfile
import urllib.parse

def connect_mac_share(share_name):
    """Connect to Mac share using different methods"""
    server = "**************"
    username = "<PERSON>"
    password = "jsthogn"
    
    print(f"Connecting to {share_name} on {server}...")
    
    # Method 1: Use expect to handle interactive authentication
    try:
        expect_script = f'''#!/usr/bin/env expect -f
set timeout 30
spawn gio mount smb://{server}/{urllib.parse.quote(share_name)}
expect {{
    "User*" {{
        send "{username}\\r"
        expect "Password*"
        send "{password}\\r"
        expect eof
    }}
    "Password*" {{
        send "{password}\\r"
        expect eof
    }}
    "Domain*" {{
        send "WORKGROUP\\r"
        expect eof
    }}
    timeout {{
        puts "Connection timed out"
        exit 1
    }}
}}
'''
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.exp', delete=False) as f:
            f.write(expect_script)
            expect_file = f.name
        
        os.chmod(expect_file, 0o755)
        
        # Check if expect is available
        result = subprocess.run(['which', 'expect'], capture_output=True)
        if result.returncode == 0:
            print("Using expect for authentication...")
            result = subprocess.run([expect_file], capture_output=True, text=True, timeout=30)
            
            # Clean up
            os.unlink(expect_file)
            
            # Check if mount succeeded
            time.sleep(2)
            gvfs_path = f"/run/user/{os.getuid()}/gvfs/smb-share:server={server},share={urllib.parse.quote(share_name)}"
            if os.path.exists(gvfs_path):
                print(f"✅ Successfully connected to {share_name}")
                print(f"   Mount point: {gvfs_path}")
                return gvfs_path
            else:
                print(f"❌ Mount failed with expect method")
        else:
            print("expect not available, trying other methods...")
            os.unlink(expect_file)
            
    except Exception as e:
        print(f"Expect method failed: {e}")
        try:
            os.unlink(expect_file)
        except:
            pass
    
    # Method 2: Try with GVFS directly
    try:
        print("Trying direct GVFS mount...")
        env = os.environ.copy()
        env['SMB_USER'] = username
        env['SMB_PASS'] = password
        
        result = subprocess.run([
            'gvfs-mount', 
            f'smb://{server}/{urllib.parse.quote(share_name)}'
        ], capture_output=True, text=True, timeout=20, env=env)
        
        time.sleep(2)
        gvfs_path = f"/run/user/{os.getuid()}/gvfs/smb-share:server={server},share={urllib.parse.quote(share_name)}"
        if os.path.exists(gvfs_path):
            print(f"✅ Successfully connected to {share_name}")
            print(f"   Mount point: {gvfs_path}")
            return gvfs_path
            
    except Exception as e:
        print(f"GVFS method failed: {e}")
    
    # Method 3: Manual instructions
    print(f"\n❌ Automatic connection failed for {share_name}")
    print("Please connect manually:")
    print("1. Open file manager (Ctrl+Alt+F)")
    print("2. Press Ctrl+L")
    print(f"3. Type: smb://{server}/{share_name}")
    print("4. Enter credentials:")
    print(f"   Username: {username}")
    print(f"   Password: {password}")
    print("   Domain: WORKGROUP")
    
    return None

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 connect_mac_share.py <share_name>")
        print("Available shares: 'Home Theater', 'Macintosh HD', 'Michael Nichols's Public Folder', 'michaelnichols', 'Theater 3'")
        sys.exit(1)
    
    share_name = sys.argv[1]
    connect_mac_share(share_name)