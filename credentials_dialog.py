import tkinter as tk
from tkinter import simpledialog

class CredentialsDialog(simpledialog.Dialog):
    def __init__(self, parent, title="SMB Credentials", server=None, share=None):
        self.username = None
        self.password = None
        self.server = server
        self.share = share
        super().__init__(parent, title)

    def body(self, master):
        row = 0
        if self.server:
            tk.Label(master, text=f"Server: {self.server}").grid(row=row, column=0, columnspan=2)
            row += 1
        if self.share:
            tk.Label(master, text=f"Share: {self.share}").grid(row=row, column=0, columnspan=2)
            row += 1
        tk.Label(master, text="Username:").grid(row=row, column=0)
        self.username_entry = tk.Entry(master)
        self.username_entry.grid(row=row, column=1)
        row += 1
        tk.Label(master, text="Password:").grid(row=row, column=0)
        self.password_entry = tk.Entry(master, show="*")
        self.password_entry.grid(row=row, column=1)
        row += 1
        self.guest_var = tk.IntVar()
        tk.Checkbutton(master, text="Guest (no password)", variable=self.guest_var, command=self.toggle_guest).grid(row=row, column=0, columnspan=2)
        return self.username_entry

    def toggle_guest(self):
        if self.guest_var.get():
            self.username_entry.delete(0, tk.END)
            self.password_entry.delete(0, tk.END)
            self.username_entry.config(state=tk.DISABLED)
            self.password_entry.config(state=tk.DISABLED)
        else:
            self.username_entry.config(state=tk.NORMAL)
            self.password_entry.config(state=tk.NORMAL)

    def apply(self):
        if self.guest_var.get():
            self.username = "guest"
            self.password = ""
        else:
            self.username = self.username_entry.get()
            self.password = self.password_entry.get()
