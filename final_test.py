#!/usr/bin/env python3
"""
Final test to verify the updated LAN file transfer app can access the Home Theater share
"""
import subprocess
import urllib.parse

def test_final_theater_connection():
    server = "192.168.68.112"
    share = "Theater"
    username = "<PERSON>"
    password = "jsthogn"
    workgroup = "WORKGROUP"
    
    print("=== Final Home Theater Connection Test ===")
    
    # Test 1: Workgroup authentication for share listing
    print(f"\n1. Testing workgroup authentication...")
    auth_string = f"{workgroup}\\{username}%{password}"
    try:
        result = subprocess.run([
            "smbclient", "-L", server, "-U", auth_string
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Share listing successful with workgroup auth")
            print("Available shares:")
            lines = result.stdout.splitlines()
            started = False
            for line in lines:
                if "Sharename" in line and "Type" in line:
                    started = True
                    continue
                if started and line.strip() and not line.startswith("-"):
                    if "Disk" in line:
                        share_name = line.split()[0]
                        print(f"  - {share_name}")
        else:
            print("❌ Share listing failed")
            print(f"Error: {result.stderr}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 2: Direct share access
    print(f"\n2. Testing direct share access...")
    try:
        result = subprocess.run([
            "smbclient", f"//{server}/{share}", "-U", auth_string,
            "-c", "ls"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Direct share access successful!")
            print(f"Contents of '{share}' share:")
            lines = result.stdout.splitlines()
            display_lines = lines[10:20]  # Show first few items
            for line in display_lines:
                if line.strip():
                    print(f"  {line}")
        else:
            print("❌ Direct share access failed")
            print(f"Error: {result.stderr}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 3: Generate mount URL (simulating what the app does)
    print(f"\n3. Testing mount URL generation...")
    try:
        # Same URL encoding logic as the app
        theater_auth_username = f"WORKGROUP%5C{urllib.parse.quote('Michael Nichols')}"
        share_url = f"smb://{theater_auth_username}:jsthogn@{server}/{share.replace(' ', '%20')}"
        print(f"Generated mount URL: {share_url.replace(':jsthogn', ':*****')}")
        print("✅ URL generation successful")
    except Exception as e:
        print(f"❌ URL generation failed: {e}")
    
    print(f"\n=== Summary ===")
    print("The LAN File Transfer app has been updated with:")
    print(f"• Server: {server}")
    print(f"• Share: {share}")
    print(f"• Username: {username}")
    print(f"• Workgroup: {workgroup}")
    print(f"• Home Theater button updated to use IP instead of hostname")
    print(f"• Authentication logic updated to include workgroup")
    print(f"• Shares list updated with correct Theater share name")

if __name__ == "__main__":
    test_final_theater_connection()