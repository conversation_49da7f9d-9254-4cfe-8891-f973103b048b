#!/usr/bin/env python3
"""
Simple LAN File Transfer - Dual Pane Browser
Shows connected/authenticated servers in both panes for easy file transfers
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import subprocess
import urllib.parse
import shutil
import threading
import time

class LanFileTransfer:
    def __init__(self, root):
        self.root = root
        self.root.title("LAN File Transfer - Connected Servers")
        self.root.geometry("1200x700")
        
        # Variables
        self.left_path = tk.StringVar(value=os.path.expanduser("~/Downloads"))
        self.right_path = tk.StringVar(value=os.path.expanduser("~/Downloads"))
        self.status_text = tk.StringVar(value="Ready")
        
        self.setup_ui()
        self.refresh_both_panes()
        
    def setup_ui(self):
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="LAN File Transfer - Connected Servers", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # Paned window for dual panes
        self.paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        
        # Left pane
        self.setup_left_pane()
        
        # Center buttons
        self.setup_center_buttons()
        
        # Right pane
        self.setup_right_pane()
        
        # Bottom status
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(status_frame, textvariable=self.status_text).pack(side=tk.LEFT)
        ttk.Button(status_frame, text="Refresh Both", 
                  command=self.refresh_both_panes).pack(side=tk.RIGHT)
        
    def setup_left_pane(self):
        left_frame = ttk.LabelFrame(self.paned_window, text="📁 Left Pane", padding="5")
        self.paned_window.add(left_frame, weight=1)
        
        # Path bar
        path_frame = ttk.Frame(left_frame)
        path_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(path_frame, text="Path:").pack(side=tk.LEFT)
        self.left_path_entry = ttk.Entry(path_frame, textvariable=self.left_path)
        self.left_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        ttk.Button(path_frame, text="Go", command=self.refresh_left_pane).pack(side=tk.RIGHT)
        
        # File list
        self.left_listbox = tk.Listbox(left_frame, selectmode=tk.EXTENDED)
        self.left_listbox.pack(fill=tk.BOTH, expand=True)
        self.left_listbox.bind('<Double-1>', self.on_left_double_click)
        
        # Quick buttons
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(button_frame, text="Connected Servers", 
                  command=self.show_connected_left).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Home", 
                  command=lambda: self.go_to_path_left(os.path.expanduser("~"))).pack(side=tk.LEFT)
        
    def setup_right_pane(self):
        right_frame = ttk.LabelFrame(self.paned_window, text="📁 Right Pane", padding="5")
        self.paned_window.add(right_frame, weight=1)
        
        # Path bar
        path_frame = ttk.Frame(right_frame)
        path_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(path_frame, text="Path:").pack(side=tk.LEFT)
        self.right_path_entry = ttk.Entry(path_frame, textvariable=self.right_path)
        self.right_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        ttk.Button(path_frame, text="Go", command=self.refresh_right_pane).pack(side=tk.RIGHT)
        
        # File list
        self.right_listbox = tk.Listbox(right_frame, selectmode=tk.EXTENDED)
        self.right_listbox.pack(fill=tk.BOTH, expand=True)
        self.right_listbox.bind('<Double-1>', self.on_right_double_click)
        
        # Quick buttons
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(button_frame, text="Connected Servers", 
                  command=self.show_connected_right).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Home", 
                  command=lambda: self.go_to_path_right(os.path.expanduser("~"))).pack(side=tk.LEFT)
        
    def setup_center_buttons(self):
        center_frame = ttk.Frame(self.paned_window, width=120)
        self.paned_window.add(center_frame, weight=0)
        
        # Center the buttons vertically
        button_frame = ttk.Frame(center_frame)
        button_frame.pack(expand=True)
        
        ttk.Button(button_frame, text="→\nCopy\nRight", 
                  command=self.copy_to_right, width=12).pack(pady=5)
        ttk.Button(button_frame, text="←\nCopy\nLeft", 
                  command=self.copy_to_left, width=12).pack(pady=5)
        
        ttk.Separator(button_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="🔄\nRefresh\nBoth", 
                  command=self.refresh_both_panes, width=12).pack(pady=5)
        
    def get_connected_shares(self):
        """Get list of currently connected SMB shares from gvfs - fast version"""
        connected = {}
        gvfs_base = f"/run/user/{os.getuid()}/gvfs"

        if not os.path.exists(gvfs_base):
            return connected

        try:
            # Get all items at once
            items = os.listdir(gvfs_base)

            for item in items:
                if "smb-share:server=" in item:
                    try:
                        # Extract server and share name
                        parts = item.split("server=")[1].split(",")
                        server = parts[0]
                        share = urllib.parse.unquote(parts[1].split("share=")[1])

                        mount_point = os.path.join(gvfs_base, item)

                        # Quick check - just verify path exists (don't check access to avoid lag)
                        if os.path.exists(mount_point):
                            if server not in connected:
                                connected[server] = []
                            connected[server].append({
                                'name': share,
                                'path': mount_point,
                                'display_name': f"{server}/{share}"
                            })
                    except Exception as e:
                        print(f"Error parsing {item}: {e}")
                        continue
        except Exception as e:
            print(f"Error reading gvfs: {e}")

        return connected
        
    def show_connected_left(self):
        """Show connected servers in left pane - improved version"""
        self.left_listbox.delete(0, tk.END)

        # Show loading message
        self.left_listbox.insert(tk.END, "🔄 Loading connected shares...")
        self.root.update_idletasks()

        connected = self.get_connected_shares()
        self.left_listbox.delete(0, tk.END)  # Clear loading message

        if not connected:
            self.left_listbox.insert(tk.END, "❌ No connected servers found")
            self.left_listbox.insert(tk.END, "")
            self.left_listbox.insert(tk.END, "📋 To connect to a server:")
            self.left_listbox.insert(tk.END, "   1. Open file manager (Ctrl+Alt+F)")
            self.left_listbox.insert(tk.END, "   2. Go to 'Other Locations'")
            self.left_listbox.insert(tk.END, "   3. Enter: smb://192.168.x.x")
            self.left_listbox.insert(tk.END, "   4. Enter your credentials")
            self.left_listbox.insert(tk.END, "   5. Click 'Refresh Both' here")
            self.left_path.set("Connected Servers")
            self.status_text.set("No connected shares - connect via file manager first")
            return

        # Show all connected shares directly (flattened list for better performance)
        self.left_listbox.insert(tk.END, "🔗 CONNECTED SHARES (Double-click to browse)")
        self.left_listbox.insert(tk.END, "")

        total_shares = 0
        for server, shares in sorted(connected.items()):
            for share in shares:
                total_shares += 1
                # Show each share as a direct browsable item
                display_text = f"📂 {share['display_name']} ✓"
                self.left_listbox.insert(tk.END, display_text)

        self.left_path.set("Connected Servers")
        self.status_text.set(f"✅ {total_shares} shares connected from {len(connected)} servers")
        
    def show_connected_right(self):
        """Show connected servers in right pane"""
        self.right_listbox.delete(0, tk.END)
        connected = self.get_connected_shares()
        
        if not connected:
            self.right_listbox.insert(tk.END, "No connected servers found")
            self.right_listbox.insert(tk.END, "")
            self.right_listbox.insert(tk.END, "To connect to a server:")
            self.right_listbox.insert(tk.END, "1. Open file manager")
            self.right_listbox.insert(tk.END, "2. Go to 'Other Locations'")
            self.right_listbox.insert(tk.END, "3. Enter: smb://server-ip")
            self.right_listbox.insert(tk.END, "4. Enter credentials")
            self.right_listbox.insert(tk.END, "5. Click Refresh Both")
            self.right_path.set("Connected Servers")
            return
            
        self.right_listbox.insert(tk.END, "🔗 CONNECTED SERVERS")
        self.right_listbox.insert(tk.END, "")
        
        for server, shares in connected.items():
            self.right_listbox.insert(tk.END, f"🖥️ {server}")
            for share in shares:
                self.right_listbox.insert(tk.END, f"  📂 {share['name']}")
                
        self.right_path.set("Connected Servers")
        self.status_text.set(f"Found {len(connected)} connected servers")

    def populate_file_list(self, listbox, path):
        """Populate a listbox with files and folders from the given path"""
        listbox.delete(0, tk.END)

        try:
            if not os.path.exists(path):
                listbox.insert(tk.END, f"Path not found: {path}")
                return

            if not os.access(path, os.R_OK):
                listbox.insert(tk.END, f"Access denied: {path}")
                return

            # Add parent directory option
            if path != "/":
                listbox.insert(tk.END, "📁 ..")

            # Get all items
            items = []
            try:
                for item in os.listdir(path):
                    if item.startswith('.'):
                        continue
                    item_path = os.path.join(path, item)
                    if os.path.isdir(item_path):
                        items.append(f"📁 {item}")
                    else:
                        items.append(f"📄 {item}")
            except PermissionError:
                listbox.insert(tk.END, "Permission denied")
                return

            # Sort: folders first, then files
            folders = [item for item in items if item.startswith("📁")]
            files = [item for item in items if item.startswith("📄")]

            for item in sorted(folders) + sorted(files):
                listbox.insert(tk.END, item)

        except Exception as e:
            listbox.insert(tk.END, f"Error: {str(e)}")

    def refresh_left_pane(self):
        """Refresh the left pane"""
        path = self.left_path.get()
        if path == "Connected Servers":
            self.show_connected_left()
        else:
            self.populate_file_list(self.left_listbox, path)

    def refresh_right_pane(self):
        """Refresh the right pane"""
        path = self.right_path.get()
        if path == "Connected Servers":
            self.show_connected_right()
        else:
            self.populate_file_list(self.right_listbox, path)

    def refresh_both_panes(self):
        """Refresh both panes"""
        self.refresh_left_pane()
        self.refresh_right_pane()

        # Update status with connection count
        connected = self.get_connected_shares()
        total_shares = sum(len(shares) for shares in connected.values())
        if total_shares > 0:
            self.status_text.set(f"✅ {total_shares} shares connected from {len(connected)} servers")
        else:
            self.status_text.set("No connected servers - use file manager to connect first")

    def go_to_path_left(self, path):
        """Navigate left pane to path"""
        self.left_path.set(path)
        self.refresh_left_pane()

    def go_to_path_right(self, path):
        """Navigate right pane to path"""
        self.right_path.set(path)
        self.refresh_right_pane()

    def on_left_double_click(self, event):
        """Handle double-click in left pane"""
        selection = self.left_listbox.curselection()
        if not selection:
            return

        item = self.left_listbox.get(selection[0])
        current_path = self.left_path.get()

        if current_path == "Connected Servers":
            # Handle connected server navigation
            if item.startswith("  📂"):
                # This is a share, navigate to it
                share_name = item.strip().replace("📂 ", "")
                connected = self.get_connected_shares()
                for server, shares in connected.items():
                    for share in shares:
                        if share['name'] == share_name:
                            self.go_to_path_left(share['path'])
                            return
        else:
            # Handle regular file/folder navigation
            if item == "📁 ..":
                parent = os.path.dirname(current_path)
                self.go_to_path_left(parent)
            elif item.startswith("📁"):
                folder_name = item.replace("📁 ", "")
                new_path = os.path.join(current_path, folder_name)
                self.go_to_path_left(new_path)

    def on_right_double_click(self, event):
        """Handle double-click in right pane"""
        selection = self.right_listbox.curselection()
        if not selection:
            return

        item = self.right_listbox.get(selection[0])
        current_path = self.right_path.get()

        if current_path == "Connected Servers":
            # Handle connected server navigation
            if item.startswith("  📂"):
                # This is a share, navigate to it
                share_name = item.strip().replace("📂 ", "")
                connected = self.get_connected_shares()
                for server, shares in connected.items():
                    for share in shares:
                        if share['name'] == share_name:
                            self.go_to_path_right(share['path'])
                            return
        else:
            # Handle regular file/folder navigation
            if item == "📁 ..":
                parent = os.path.dirname(current_path)
                self.go_to_path_right(parent)
            elif item.startswith("📁"):
                folder_name = item.replace("📁 ", "")
                new_path = os.path.join(current_path, folder_name)
                self.go_to_path_right(new_path)

    def get_selected_files(self, listbox, current_path):
        """Get selected files from a listbox"""
        if current_path == "Connected Servers":
            return []

        selected_files = []
        for index in listbox.curselection():
            item = listbox.get(index)
            if item.startswith("📁 .."):
                continue

            if item.startswith("📁 ") or item.startswith("📄 "):
                filename = item[2:].strip()  # Remove emoji and space
                file_path = os.path.join(current_path, filename)
                selected_files.append(file_path)

        return selected_files

    def copy_to_right(self):
        """Copy selected files from left to right pane"""
        left_path = self.left_path.get()
        right_path = self.right_path.get()

        if left_path == "Connected Servers" or right_path == "Connected Servers":
            messagebox.showwarning("Copy Error",
                "Please navigate to actual folders before copying files")
            return

        selected_files = self.get_selected_files(self.left_listbox, left_path)
        if not selected_files:
            messagebox.showwarning("Copy Error", "Please select files to copy")
            return

        self.copy_files(selected_files, right_path, "right")

    def copy_to_left(self):
        """Copy selected files from right to left pane"""
        left_path = self.left_path.get()
        right_path = self.right_path.get()

        if left_path == "Connected Servers" or right_path == "Connected Servers":
            messagebox.showwarning("Copy Error",
                "Please navigate to actual folders before copying files")
            return

        selected_files = self.get_selected_files(self.right_listbox, right_path)
        if not selected_files:
            messagebox.showwarning("Copy Error", "Please select files to copy")
            return

        self.copy_files(selected_files, left_path, "left")

    def copy_files(self, source_files, dest_dir, direction):
        """Copy files in a separate thread"""
        def copy_thread():
            try:
                self.status_text.set(f"Copying {len(source_files)} files...")
                self.root.update_idletasks()

                copied_count = 0
                for source_file in source_files:
                    if not os.path.exists(source_file):
                        continue

                    filename = os.path.basename(source_file)
                    dest_file = os.path.join(dest_dir, filename)

                    if os.path.isdir(source_file):
                        # Copy directory
                        if os.path.exists(dest_file):
                            shutil.rmtree(dest_file)
                        shutil.copytree(source_file, dest_file)
                    else:
                        # Copy file
                        shutil.copy2(source_file, dest_file)

                    copied_count += 1
                    self.status_text.set(f"Copied {copied_count}/{len(source_files)} files...")
                    self.root.update_idletasks()

                # Refresh the destination pane
                if direction == "right":
                    self.root.after(0, self.refresh_right_pane)
                else:
                    self.root.after(0, self.refresh_left_pane)

                self.status_text.set(f"✅ Successfully copied {copied_count} files")

            except Exception as e:
                self.status_text.set(f"❌ Copy error: {str(e)}")
                messagebox.showerror("Copy Error", f"Error copying files: {str(e)}")

        # Start copy in background thread
        threading.Thread(target=copy_thread, daemon=True).start()

if __name__ == "__main__":
    root = tk.Tk()
    app = LanFileTransfer(root)

    # Show connected servers in both panes on startup
    app.show_connected_left()
    app.show_connected_right()

    root.mainloop()
