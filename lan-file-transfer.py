#!/usr/bin/env python3

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import os
import shutil
import subprocess
import threading
import time
import concurrent.futures
from pathlib import Path
from credentials_dialog import CredentialsDialog
import urllib.parse

class ToolTip:
    """A simple tooltip class for tkinter widgets"""
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.show_tooltip)
        self.widget.bind("<Leave>", self.hide_tooltip)

    def show_tooltip(self, event=None):
        """Show the tooltip"""
        x, y, _, _ = self.widget.bbox("insert")
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() + 20
        
        # Create a toplevel window
        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_geometry(f"+{x}+{y}")
        
        # Add a label with the tooltip text
        label = ttk.Label(
            self.tooltip, 
            text=self.text, 
            background="#ffffe0", 
            relief="solid", 
            borderwidth=1,
            padding=5
        )
        label.pack()
        
        # Make sure tooltip stays on top
        self.tooltip.wm_attributes('-topmost', True)

    def hide_tooltip(self, event=None):
        """Hide the tooltip"""
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None

class DualPaneFileTransfer:
    def __init__(self, root):
        self.root = root
        self.root.title("LAN File Transfer - Dual Pane Browser")
        self.root.geometry("1400x800")
        
        # Configuration
        self.items_per_page = 1000  # Adjustable pagination size
        self.max_items_before_pagination = 1000  # When to start paginating
        self.chunk_size = 1024 * 1024  # 1MB chunks for better performance
        self.max_workers = 4  # Parallel transfer workers
        
        # Variables
        self.transfer_progress = tk.DoubleVar()
        self.status_text = tk.StringVar(value="Ready")
        self.current_file_count = 0
        self.total_file_count = 0
        self.transfer_start_time = None
        self.bytes_transferred = 0
        self.total_bytes = 0
        
        # Current paths for left and right panes
        self.left_path = tk.StringVar(value=os.path.expanduser("~/Downloads"))
        self.right_path = tk.StringVar(value="Network")  # Start at Network root for manual navigation
        
        self.setup_ui()
        self.configure_tree_styles()
        self.setup_mac_shares_right_pane()
        # Show startup message
        self.root.after(1000, self.show_startup_help)
        
    def configure_tree_styles(self):
        """Configure visual styles for tree view items"""
        # Configure tags for different connection states
        self.left_tree.tag_configure("connected", foreground="green", font=("Arial", 9, "bold"))
        self.left_tree.tag_configure("disconnected", foreground="gray")
        self.left_tree.tag_configure("connected_section", foreground="blue", font=("Arial", 10, "bold"))
        self.left_tree.tag_configure("network_section", foreground="navy", font=("Arial", 10, "bold"))
        self.left_tree.tag_configure("connected_server", foreground="darkgreen", font=("Arial", 9, "bold"))
        self.left_tree.tag_configure("connected_share", foreground="green")

        self.right_tree.tag_configure("connected", foreground="green", font=("Arial", 9, "bold"))
        self.right_tree.tag_configure("disconnected", foreground="gray")
        self.right_tree.tag_configure("connected_section", foreground="blue", font=("Arial", 10, "bold"))
        self.right_tree.tag_configure("network_section", foreground="navy", font=("Arial", 10, "bold"))
        self.right_tree.tag_configure("connected_server", foreground="darkgreen", font=("Arial", 9, "bold"))
        self.right_tree.tag_configure("connected_share", foreground="green")
        self.right_tree.tag_configure("error", foreground="red")
        self.right_tree.tag_configure("info", foreground="blue")

    def show_startup_help(self):
        """Show help message on startup"""
        self.status_text.set("Connected shares shown at top - click any share to browse")
        
    def setup_ui(self):
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="LAN File Transfer - Dual Pane Browser", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # Paned window for dual panes
        self.paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        
        # Left pane (Local files)
        self.setup_left_pane()
        
        # Center buttons
        self.setup_center_buttons()
        
        # Right pane (Network drives)
        self.setup_right_pane()
        
        # Bottom status and progress
        self.setup_bottom_panel(main_frame)
        
    def setup_left_pane(self):
        left_frame = ttk.LabelFrame(self.paned_window, text="📁 Files & Shares", padding="5")
        self.paned_window.add(left_frame, weight=1)
        
        # Navigation bar
        left_nav = ttk.Frame(left_frame)
        left_nav.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(left_nav, text="🏠", command=self.go_home_left, width=3).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(left_nav, text="↑", command=self.go_up_left, width=3).pack(side=tk.LEFT, padx=(0, 2))
        
        self.left_path_entry = ttk.Entry(left_nav, textvariable=self.left_path)
        self.left_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        self.left_path_entry.bind('<Return>', lambda e: self.refresh_left_pane())
        
        ttk.Button(left_nav, text="Go", command=self.refresh_left_pane).pack(side=tk.RIGHT)
        
        # File tree
        left_tree_frame = ttk.Frame(left_frame)
        left_tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.left_tree = ttk.Treeview(left_tree_frame, columns=("#1",), show="tree")
        self.left_tree.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        self.left_tree.bind("<Double-1>", lambda e: self._handle_double_click(self.left_tree, self.left_path, self.refresh_left_pane))
        self.left_tree.bind("<Return>", lambda e: self._handle_double_click(self.left_tree, self.left_path, self.refresh_left_pane))
        self.left_tree.bind("<<TreeviewOpen>>", lambda e: self._on_tree_expand(self.left_tree, self.left_path, left=True))
        
        # Scrollbars for left tree
        left_v_scroll = ttk.Scrollbar(left_tree_frame, orient=tk.VERTICAL, command=self.left_tree.yview)
        self.left_tree.configure(yscrollcommand=left_v_scroll.set)
        
        left_v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        left_tree_frame.grid_rowconfigure(0, weight=1)
        left_tree_frame.grid_columnconfigure(0, weight=1)
        
        self.left_tree.bind("<Prior>", lambda e: self._handle_page_key(self.left_tree, self.refresh_left_pane, "prev"))
        self.left_tree.bind("<Next>", lambda e: self._handle_page_key(self.left_tree, self.refresh_left_pane, "next"))
        
        # Quick access buttons
        left_quick = ttk.Frame(left_frame)
        left_quick.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(left_quick, text="Network", command=lambda: self.go_to_path_left("Network")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_quick, text="Downloads", command=lambda: self.go_to_path_left(os.path.expanduser("~/Downloads"))).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_quick, text="Documents", command=lambda: self.go_to_path_left(os.path.expanduser("~/Documents"))).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_quick, text="Desktop", command=lambda: self.go_to_path_left(os.path.expanduser("~/Desktop"))).pack(side=tk.LEFT)

    def populate_network_browser_left(self):
        """Populate the left tree with connected shares first, then all discovered SMB hosts."""
        self.left_tree.delete(*self.left_tree.get_children())

        # Get connected shares first
        connected_shares = self.get_connected_shares()

        # Add Connected Shares section if any exist
        if connected_shares:
            connected_node = self.left_tree.insert("", "end", text="🔗 Connected Shares",
                                                 values=("", ""), tags=("connected_section",))

            for server, shares in connected_shares.items():
                # Add server node
                server_node = self.left_tree.insert(connected_node, "end",
                                                  text=f"🖥️ {server} (Connected)",
                                                  values=("", ""),
                                                  tags=("connected_server", server, "connected_server"))

                # Add each connected share
                for share_info in shares:
                    share_name = share_info['name']
                    mount_point = share_info['mount_point']
                    self.left_tree.insert(server_node, "end",
                                        text=f"📂 {share_name} ✓",
                                        values=("Connected Share", mount_point),
                                        tags=("connected_share", mount_point, share_name, "connected", "connected_share"))

            # Expand connected shares by default
            self.left_tree.item(connected_node, open=True)
            for child in self.left_tree.get_children(connected_node):
                self.left_tree.item(child, open=True)

        # Get all network shares for discovery
        network = self.get_all_network_shares()

        if network:
            # Add Network Discovery section
            network_node = self.left_tree.insert("", "end", text="🌐 Network Discovery",
                                               values=("", ""), tags=("network_section", "network_section"))

            # Add each computer and its shares to the tree
            for computer, shares in network.items():
                # Check if this computer is already shown in connected shares
                is_connected = computer.upper() in [s.upper() for s in connected_shares.keys()]

                # Add computer as child of Network node
                computer_icon = "🖥️" if is_connected else "💻"
                computer_text = f"{computer_icon} {computer}"
                if is_connected:
                    computer_text += " (Some shares connected)"

                computer_node = self.left_tree.insert(network_node, "end",
                                                    text=computer_text,
                                                    values=("", ""),
                                                    tags=("computer", computer))

                # Add each share as a child of the computer node
                for share in sorted(shares):
                    # Check if this specific share is connected
                    share_connected = is_connected and any(
                        s['name'] == share for s in connected_shares.get(computer.upper(), [])
                    )

                    share_icon = "📂" if share_connected else "📁"
                    share_text = f"{share_icon} {share}"
                    if share_connected:
                        share_text += " ✓"

                    mount_point = f"smb://{computer}/{share}"
                    self.left_tree.insert(computer_node, "end",
                                        text=share_text,
                                        values=("Share", mount_point),
                                        tags=("share", mount_point, share, "connected" if share_connected else "disconnected"))

            # Expand the network section by default
            self.left_tree.item(network_node, open=True)

        # If no connected shares and no network discovery, show message
        if not connected_shares and not network:
            self.left_tree.insert("", "end", text="No network computers or connected shares found",
                                values=("", ""), tags=("info",))

        self.left_path.set("Network")
        
    def _on_tree_expand(self, tree, path_var, left):
        # Called when a node is expanded; if it's a computer node, enumerate shares
        selection = tree.selection()
        if not selection:
            return
        item = selection[0]
        tags = tree.item(item, "tags")
        if not tags or tags[0] != "computer":
            return
        host = tags[1]
        # Remove dummy children
        for child in tree.get_children(item):
            if "dummy" in tree.item(child, "tags"):
                tree.delete(child)
        # If shares already loaded, do not reload
        if any("share" in tree.item(child, "tags") for child in tree.get_children(item)):
            return
        # Enumerate shares with smbclient
        import subprocess
        import urllib.parse
        shares = []
        try:
            out = subprocess.check_output(["smbclient", "-L", host, "-N"], stderr=subprocess.STDOUT, text=True, timeout=7)
            found_section = False
            for line in out.splitlines():
                if "Sharename" in line and "Type" in line:
                    found_section = True
                    continue
                if found_section:
                    if line.strip() == "" or line.startswith("----"):
                        continue
                    parts = line.split()
                    if len(parts) > 1 and parts[1] == "Disk":
                        shares.append(parts[0])
        except Exception as e:
            # Could not enumerate shares anonymously; try credentials
            # Heuristic: if host contains 'mac', use Mac creds; if 'win', use Windows creds; else try both
            mac_hosts = ["michaels-mac-pro-game-room.local"]
            win_hosts = ["*************", "popzvps", "popz-theater.local", "**************"]
            mac_username = "Michael Nichols"
            mac_password = "jsthogn"
            win_username = "michael5cents"
            win_password = "5904"
            theater_username = "Michael Nichols"
            theater_password = "jsthogn"
            theater_workgroup = "WORKGROUP"
            tried = False
            import re
            shares = []
            # Try Mac credentials
            if any(h in host for h in mac_hosts) or re.search(r"mac", host, re.I):
                try:
                    out = subprocess.check_output(["smbclient", "-L", host, "-U", f"{mac_username}%{mac_password}"], stderr=subprocess.STDOUT, text=True, timeout=7)
                    found_section = False
                    for line in out.splitlines():
                        if "Sharename" in line and "Type" in line:
                            found_section = True
                            continue
                        if found_section:
                            if line.strip() == "" or line.startswith("----"):
                                continue
                            parts = line.split()
                            if len(parts) > 1 and parts[1] == "Disk":
                                shares.append(parts[0])
                    tried = True
                except Exception as e2:
                    pass
            # Try Windows credentials for POPZVPS
            if (not shares) and (any(h in host for h in ["*************", "popzvps"]) or host == "*************"):
                try:
                    out = subprocess.check_output(["smbclient", "-L", host, "-U", f"{win_username}%{win_password}"], stderr=subprocess.STDOUT, text=True, timeout=7)
                    found_section = False
                    for line in out.splitlines():
                        if "Sharename" in line and "Type" in line:
                            found_section = True
                            continue
                        if found_section:
                            if line.strip() == "" or line.startswith("----"):
                                continue
                            parts = line.split()
                            if len(parts) > 1 and parts[1] == "Disk":
                                shares.append(parts[0])
                    tried = True
                except Exception as e3:
                    pass
            # Try Theater credentials for Home Theater computer
            if (not shares) and (host == "**************" or "popz-theater.local" in host):
                try:
                    # Use workgroup in username format: WORKGROUP\username
                    theater_auth = f"{theater_workgroup}\\{urllib.parse.quote(theater_username)}%{theater_password}"
                    out = subprocess.check_output(["smbclient", "-L", host, "-U", theater_auth], stderr=subprocess.STDOUT, text=True, timeout=7)
                    found_section = False
                    for line in out.splitlines():
                        if "Sharename" in line and "Type" in line:
                            found_section = True
                            continue
                        if found_section:
                            if line.strip() == "" or line.startswith("----"):
                                continue
                            parts = line.split()
                            if len(parts) > 1 and parts[1] == "Disk":
                                shares.append(parts[0])
                    tried = True
                except Exception as e3:
                    pass
            if shares:
                for share in shares:
                    share_path = f"/run/user/1000/gvfs/smb-share:server={host},share={{}}".format(urllib.parse.quote(share))
                    tree.insert(item, "end", text=f"📁 {share}", values=("", ""), tags=("share", share_path))
                return
            # If tried creds and still none, show error
            if tried:
                tree.insert(item, "end", text=f"❌ Could not list shares (even with credentials)", values=("", ""))
            else:
                tree.insert(item, "end", text=f"❌ Could not list shares (may require credentials)", values=("", ""))
            return
        if not shares:
            tree.insert(item, "end", text=f"No shares found.", values=("", ""))
            return
        for share in shares:
            share_path = f"/run/user/1000/gvfs/smb-share:server={host},share={urllib.parse.quote(share)}"
            tree.insert(item, "end", text=f"📁 {share}", values=("", ""), tags=("share", share_path))

    def setup_center_buttons(self):
        center_frame = ttk.Frame(self.paned_window, width=100)
        self.paned_window.add(center_frame, weight=0)
        
        # Center the buttons vertically
        button_frame = ttk.Frame(center_frame)
        button_frame.pack(expand=True)
        
        # Transfer buttons
        ttk.Button(button_frame, text="→\nTransfer\nRight", command=self.transfer_to_right, width=12).pack(pady=5)
        ttk.Button(button_frame, text="←\nTransfer\nLeft", command=self.transfer_to_left, width=12).pack(pady=5)
        
        ttk.Separator(button_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="🔄\nRefresh\nBoth", command=self.refresh_both_panes, width=12).pack(pady=5)
        
    def setup_right_pane(self):
        right_frame = ttk.LabelFrame(self.paned_window, text="🌐 Network Drives", padding="5")
        self.paned_window.add(right_frame, weight=1)
        
        # Navigation bar
        right_nav = ttk.Frame(right_frame)
        right_nav.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(right_nav, text="🏠", command=self.go_home_right, width=3).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(right_nav, text="↑", command=self.go_up_right, width=3).pack(side=tk.LEFT, padx=(0, 2))
        
        self.right_path_entry = ttk.Entry(right_nav, textvariable=self.right_path)
        self.right_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        self.right_path_entry.bind('<Return>', lambda e: self.refresh_right_pane())
        
        ttk.Button(right_nav, text="Go", command=self.refresh_right_pane).pack(side=tk.RIGHT)
        
        # File tree
        right_tree_frame = ttk.Frame(right_frame)
        right_tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.right_tree = ttk.Treeview(right_tree_frame, columns=("#1",), show="tree")
        self.right_tree.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        self.right_tree.bind("<Double-1>", lambda e: self._handle_double_click(self.right_tree, self.right_path, self.refresh_right_pane))
        self.right_tree.bind("<Return>", lambda e: self._handle_double_click(self.right_tree, self.right_path, self.refresh_right_pane))
        self.right_tree.bind("<<TreeviewOpen>>", lambda e: self._on_tree_expand(self.right_tree, self.right_path, left=False))
        
        # Scrollbars for right tree
        right_v_scroll = ttk.Scrollbar(right_tree_frame, orient=tk.VERTICAL, command=self.right_tree.yview)
        self.right_tree.configure(yscrollcommand=right_v_scroll.set)
        
        right_v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        right_tree_frame.grid_rowconfigure(0, weight=1)
        
        # Define common shares with their display names and paths
        common_shares = [
            ("Home Theater", "smb://**************/Home Theater"),
            ("Plex Drive", "smb://**************/Plex Drive"),
            ("Media", "smb://**************/Media"),
            ("Backups", "smb://**************/Backups"),
            ("Public", "smb://**************/Public")
        ]
        
        # Create a frame for quick access buttons
        right_quick_frame = ttk.Frame(right_frame)
        right_quick_frame.pack(fill=tk.X, pady=(5, 0))
        
        # Create buttons for common shares
        for name, path in common_shares:
            btn = ttk.Button(
                right_quick_frame, 
                text=name,
                command=lambda p=path: self.go_to_path_right(p)
            )
            btn.pack(side=tk.LEFT, padx=(0, 2))
            
            # Add tooltip with the full path
            ToolTip(btn, path)
        
    def connect_home_theater_direct(self):
        """Direct connection to Home Theater share with better error handling"""
        try:
            server = "**************"
            share = "michaelnichols"  # This is the share name, not username
            
            # First try to check if already connected
            gvfs_path = f"/run/user/{os.getuid()}/gvfs/smb-share:server={server},share={share}"
            
            if os.path.exists(gvfs_path) and os.access(gvfs_path, os.R_OK):
                self.status_text.set("✅ Home Theater already connected")
                self.go_to_path_right(gvfs_path)
                self.refresh_right_pane()
                return
            
            self.status_text.set("Connecting to Home Theater...")
            self.root.update_idletasks()
            
            # Try mounting with workgroup authentication
            import urllib.parse
            theater_auth_username = f"WORKGROUP%5C{urllib.parse.quote(theater_username)}"
            share_url = f"smb://{theater_auth_username}:jsthogn@{server}/{share}"
            
            # Use subprocess with longer timeout and better error handling
            try:
                result = subprocess.run(['gio', 'mount', share_url], 
                                      capture_output=True, text=True, timeout=20)
                
                # Wait for mount to settle
                import time
                time.sleep(3)
                
                if result.returncode == 0 or (result.stderr and "already mounted" in result.stderr.lower()):
                    if os.path.exists(gvfs_path):
                        self.status_text.set("✅ Home Theater connected")
                        self.go_to_path_right(gvfs_path)
                        # Refresh to show contents
                        self.root.after(500, self.refresh_right_pane)
                    else:
                        raise Exception("Mount succeeded but path not accessible")
                else:
                    raise Exception(f"Mount failed: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                raise Exception("Connection timeout")
                
        except Exception as e:
            self.status_text.set("❌ Home Theater connection failed")
            error_msg = str(e)
            
            # Show helpful error message with instructions
            messagebox.showwarning("Connection Issue", 
            f"Could not automatically connect to Mac share.\n\n"
            f"Error: {error_msg}\n\n"
            f"To connect manually:\n"
            f"1. Click 'Browse Network'\n"
            f"2. Select '**************'\n"
            f"3. Enter credentials when prompted:\n"
            f"   • Username: Michael Nichols\n"
            f"   • Password: jsthogn")
    
    def get_connected_shares(self):
        """Get list of currently connected SMB shares from gvfs"""
        import urllib.parse
        gvfs_base = f"/run/user/{os.getuid()}/gvfs"
        connected = {}

        if os.path.exists(gvfs_base):
            for item in os.listdir(gvfs_base):
                if "smb-share:server=" in item:
                    try:
                        # Extract server and share name from the mount point
                        parts = item.split("server=")[1].split(",")
                        server = parts[0]
                        share = urllib.parse.unquote(parts[1].split("share=")[1])

                        # Get mount point
                        mount_point = os.path.join(gvfs_base, item)

                        # Verify the mount point is accessible
                        if os.path.exists(mount_point) and os.access(mount_point, os.R_OK):
                            # Add to connected shares
                            if server not in connected:
                                connected[server] = []
                            connected[server].append({
                                'name': share,
                                'mount_point': mount_point,
                                'is_connected': True,
                                'server': server
                            })
                    except Exception as e:
                        print(f"Error parsing mount point {item}: {e}")
                        continue

        return connected

    def get_all_network_shares(self):
        """Get all available network shares from all computers on the network"""
        # This will store computer names and their shares
        network = {}
        workgroup = 'WORKGROUP'  # Default workgroup name
        
        # First, try to get the workgroup name
        try:
            # Use nmblookup to get workgroup name
            result = subprocess.run(
                ['nmblookup', '-S', '--broadcast'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'workgroup:' in line.lower():
                        workgroup = line.split(':')[-1].strip()
                        break
        except Exception as e:
            print(f"Error getting workgroup: {e}")
        
        computers = set()
        
        # First try using findsmb if available
        try:
            # Check if findsmb exists
            subprocess.run(['which', 'findsmb'], check=True, capture_output=True)
            
            # Use findsmb to discover computers in the workgroup
            result = subprocess.run(
                ['findsmb', '-w', workgroup],
                capture_output=True,
                text=True,
                timeout=15
            )
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if '\\' in line and 'Workgroup' in line:
                        computer = line.split('\\')[-1].split()[0].strip()
                        if computer and computer.upper() != workgroup.upper():
                            computers.add(computer.upper())
                            
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("findsmb not found, falling back to nmblookup only")
        
        # Fallback to nmblookup if no computers found with findsmb
        if not computers:
            try:
                result = subprocess.run(
                    ['nmblookup', '-S', '--broadcast'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if 'Looking up status of' in line:
                            computer = line.split()[-1].strip()
                            if computer and computer.upper() != workgroup.upper():
                                computers.add(computer.upper())
            except Exception as e:
                print(f"Error with nmblookup: {e}")
        
        # If still no computers found, use hardcoded values
        if not computers:
            print("No computers found via network discovery, using hardcoded values")
            computers = {'HOMETHEATER', 'POPZ-THEATER'}
        
        # Sort the computers for consistent display
        computers = sorted(computers)
        
        # Now get shares for each computer
        network = {}
        for computer in computers:
            try:
                # Use smbclient to list shares on the computer
                result = subprocess.run(
                    ['smbclient', f'//{computer}/', '-N', '-L'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    shares = []
                    for line in result.stdout.split('\n'):
                        if 'Disk' in line and 'IPC$' not in line and 'print$' not in line.lower():
                            share_name = line.split()[0].strip()
                            if share_name and not share_name.startswith('\\'):
                                shares.append(share_name)
                    if shares:
                        network[computer] = shares
            except Exception as e:
                print(f"Error getting shares for {computer}: {e}")
        
        # Fallback to hardcoded shares if discovery fails
        if not network:
            print("No computers found via network discovery, using hardcoded values")
            network = {
                "HOMETHEATER": ["Media", "Plex Drive", "Home Theater", "Backups", "Public"],
                "POPZVPS": ["michael5cents-home", "Media", "Backups"]
            }

        return network

    def setup_mac_shares_right_pane(self):
        """Set up the right pane with connected shares first, then all network computers"""
        self.right_tree.delete(*self.right_tree.get_children())

        try:
            # Show loading message
            loading_id = self.right_tree.insert(
                "", "end",
                text="🔄 Discovering network shares...",
                values=("", ""),
                tags=("info",)
            )
            self.right_tree.update()

            # Get connected shares using the improved method
            connected_shares_data = self.get_connected_shares()

            # Get all network shares
            network = self.get_all_network_shares()

            # Clear loading message
            self.right_tree.delete(loading_id)

            # Add Connected Shares section first (prioritized)
            if connected_shares_data:
                connected_node = self.right_tree.insert("", "end", text="🔗 Connected Shares",
                                                       values=("", ""), tags=("connected_section", "connected_section"))

                for server, shares in connected_shares_data.items():
                    # Add server node
                    server_node = self.right_tree.insert(connected_node, "end",
                                                        text=f"🖥️ {server} (Connected)",
                                                        values=("", ""),
                                                        tags=("connected_server", server, "connected_server"))

                    # Add each connected share with direct access
                    for share_info in shares:
                        share_name = share_info['name']
                        mount_point = share_info['mount_point']
                        self.right_tree.insert(server_node, "end",
                                             text=f"📂 {share_name} ✓",
                                             values=("Connected Share", mount_point),
                                             tags=("connected_share", mount_point, share_name, "connected", "connected_share"))

                # Expand connected shares by default
                self.right_tree.item(connected_node, open=True)
                for child in self.right_tree.get_children(connected_node):
                    self.right_tree.item(child, open=True)

            # Add Network Discovery section
            if network:
                network_node = self.right_tree.insert(
                    "", "end",
                    text="🌐 Network Discovery",
                    values=("Network", ""),
                    tags=("network_section", "network_section")
                )

                # Add each computer and its shares to the tree
                for computer, shares in sorted(network.items()):
                    try:
                        # Check if computer has connected shares
                        is_connected = computer.upper() in [s.upper() for s in connected_shares_data.keys()]

                        # Add computer node with connection status
                        computer_icon = "🖥️" if is_connected else "💻"
                        computer_text = f"{computer_icon} {computer}"
                        if is_connected:
                            computer_text += " (Some shares connected)"

                        computer_node = self.right_tree.insert(
                            network_node, "end",
                            text=computer_text,
                            values=("Computer", ""),
                            tags=("computer", computer, "connected" if is_connected else "disconnected")
                        )

                        # Add shares as children
                        for share in sorted(shares):
                            share_path = f"smb://{computer}/{share}"
                            # Check if this specific share is connected
                            share_connected = is_connected and any(
                                s['name'] == share for s in connected_shares_data.get(computer.upper(), [])
                            )

                            share_icon = "📂" if share_connected else "📁"
                            share_text = f"{share_icon} {share}"
                            if share_connected:
                                share_text += " ✓"

                            self.right_tree.insert(
                                computer_node, "end",
                                text=share_text,
                                values=("Share", share_path),
                                tags=("share", share_path, share, "connected" if share_connected else "disconnected")
                            )

                        # Expand computer node by default if it has connected shares
                        if is_connected:
                            self.right_tree.item(computer_node, open=True)

                    except Exception as e:
                        print(f"Error adding computer {computer}: {e}")
                        continue

                # Expand the network discovery section by default
                self.right_tree.item(network_node, open=True)

            # Set status and path
            if not connected_shares_data and not network:
                self.right_tree.insert(
                    "", "end",
                    text="❌ No network computers or connected shares found",
                    values=("", ""),
                    tags=("error",)
                )
                self.status_text.set("No network computers found - check your network connection")
            else:
                connected_count = len(connected_shares_data)
                network_count = len(network) if network else 0
                self.status_text.set(f"Found {connected_count} connected servers, {network_count} network computers")

            self.right_path.set("Network")

            # Auto-scroll to show the first item
            self.root.after(100, lambda: self.right_tree.see(self.right_tree.get_children()[0] if self.right_tree.get_children() else 0))
            
        except Exception as e:
            print(f"Error setting up network shares: {e}")
            self.right_tree.insert(
                "", "end", 
                text=f"❌ Error: {str(e)}", 
                values=("", ""), 
                tags=("error",)
            )
            self.right_path.set("Network")
            self.status_text.set(f"Error: {str(e)}")

    def connect_mac_share(self):
        """Connect to a specific Mac share at **************"""
        try:
            server = "**************"
            username = "Michael Nichols"
            password = "jsthogn"
            
            # Create the gvfs path
            gvfs_path = f"/run/user/{os.getuid()}/gvfs/smb-share:server={server},share={urllib.parse.quote(share_name)}"
            
            # Check if already connected
            if os.path.exists(gvfs_path) and os.access(gvfs_path, os.R_OK):
                self.status_text.set(f"✅ {share_name} already connected")
                self.go_to_path_right(gvfs_path)
                self.refresh_right_pane()
                return
            
            self.status_text.set(f"Connecting to {share_name}...")
            self.root.update_idletasks()
            
            # Try multiple connection methods for Mac compatibility
            connection_methods = [
                # Method 1: URL with encoded username and password
                f"smb://{urllib.parse.quote(username)}:{password}@{server}/{urllib.parse.quote(share_name)}",
                # Method 2: URL with percent-encoded spaces in username
                f"smb://Michael%20Nichols:{password}@{server}/{urllib.parse.quote(share_name)}",
                # Method 3: Simple URL without credentials (will prompt)
                f"smb://{server}/{urllib.parse.quote(share_name)}"
            ]
            
            for method_num, share_url in enumerate(connection_methods, 1):
                try:
                    self.status_text.set(f"Connecting to {share_name}... (method {method_num}/3)")
                    self.root.update_idletasks()
                    
                    # Use different timeouts for different methods
                    timeout = 15 if method_num <= 2 else 30
                    
                    result = subprocess.run(['gio', 'mount', share_url], 
                                          capture_output=True, text=True, timeout=timeout)
                    
                    # Wait for mount to settle
                    import time
                    time.sleep(2)
                    
                    if result.returncode == 0 or (result.stderr and "already mounted" in result.stderr.lower()):
                        if os.path.exists(gvfs_path):
                            self.status_text.set(f"✅ Connected to {share_name}")
                            self.go_to_path_right(gvfs_path)
                            self.root.after(500, self.refresh_right_pane)
                            return
                    
                    # If method failed, try next one
                    print(f"Method {method_num} failed: {result.stderr}")
                    
                except subprocess.TimeoutExpired:
                    print(f"Method {method_num} timed out")
                    continue
                except Exception as method_error:
                    print(f"Method {method_num} error: {method_error}")
                    continue
            
            # If all methods failed, show manual connection instructions
            raise Exception("All automatic connection methods failed")
                
        except Exception as e:
            self.status_text.set(f"⚠️ {share_name} - manual connection needed")
            error_msg = str(e)
            
            # Show manual connection dialog
            result = messagebox.askyesno("Manual Connection Required", 
                f"Automatic connection to {share_name} failed.\n\n"
                f"Would you like to open the manual connection helper?\n\n"
                f"It will guide you through connecting to all Mac shares.")
            
            if result:
                self.open_manual_connection()
            else:
                messagebox.showinfo("Manual Instructions", 
                    f"To connect to {share_name} manually:\n\n"
                    f"1. Open file manager (Nautilus)\n"
                    f"2. Press Ctrl+L or go to 'Other Locations'\n"
                    f"3. Enter: smb://**************\n"
                    f"4. When prompted, enter:\n"
                    f"   • Username: Michael Nichols\n"
                    f"   • Password: jsthogn\n"
                    f"   • Domain: WORKGROUP (if asked)\n\n"
                    f"After connecting, click 'Check Mounts' button")
    
    def open_manual_connection(self):
        """Open file manager for manual Mac connection with guidance"""
        try:
            self.status_text.set("Opening manual connection helper...")
            
            # Show guidance dialog first
            messagebox.showinfo("Manual Connection Guide", 
                "File manager will open to 'Other Locations'.\n\n"
                "STEPS TO CONNECT:\n"
                "1. In the 'Connect to Server' box, enter:\n"
                "   smb://**************\n\n"
                "2. Click 'Connect'\n\n"
                "3. When prompted for credentials:\n"
                "   • Username: Michael Nichols\n"
                "   • Password: jsthogn\n"
                "   • Domain: WORKGROUP (if asked)\n\n"
                "4. You'll see all 5 Mac shares available\n\n"
                "After connecting, click 'Check Mounts' to refresh")
            
            # Open file manager to Other Locations
            subprocess.Popen(['nautilus', 'other-locations:///'], 
                           stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            self.status_text.set("ℹ️ Manual connection helper opened - follow the guide")
            
            # Auto-check for mounts after 20 seconds
            self.root.after(20000, self.auto_check_mounts)
            
        except Exception as e:
            self.status_text.set("Error opening manual connection helper")
            messagebox.showerror("Error", 
                f"Could not open file manager: {e}\n\n"
                "Please open file manager manually and:\n"
                "1. Press Ctrl+L\n"
                "2. Type: smb://**************\n"
                "3. Enter credentials when prompted")
    
    def auto_check_mounts(self):
        """Automatically check for mounted shares after manual connection"""
        self.check_mounted_shares()
        if "No Mac shares" in self.status_text.get():
            # Try again in 10 seconds if no mounts found
            self.root.after(10000, self.check_mounted_shares)
    
    def check_mounted_shares(self):
        """Check which Mac shares are currently mounted and update status"""
        try:
            mounted_shares = []
            gvfs_base = f"/run/user/{os.getuid()}/gvfs"
            
            if os.path.exists(gvfs_base):
                for item in os.listdir(gvfs_base):
                    if "**************" in item and "smb-share" in item:
                        # Extract share name from the mount point
                        try:
                            share_part = item.split("share=")[1]
                            share_name = urllib.parse.unquote(share_part)
                            mounted_shares.append(share_name)
                        except:
                            continue
            
            if mounted_shares:
                self.status_text.set(f"✅ Mounted: {', '.join(mounted_shares)}")
                # Update the right pane to show mounted shares
                self.update_mac_shares_status(mounted_shares)
            else:
                self.status_text.set("No Mac shares currently mounted")
                
        except Exception as e:
            self.status_text.set(f"Error checking mounts: {str(e)}")
    
    def update_mac_shares_status(self, mounted_shares):
        """Update the Mac shares tree to show which are mounted"""
        # Find the Mac computer node
        for item in self.right_tree.get_children():
            if "Mac Pro" in self.right_tree.item(item, "text"):
                # Update child share nodes
                for child in self.right_tree.get_children(item):
                    child_text = self.right_tree.item(child, "text")
                    share_name = child_text.replace("📁 ", "")
                    if share_name in mounted_shares:
                        new_text = f"✅ {share_name} (mounted)"
                        self.right_tree.item(child, text=new_text)
                    else:
                        new_text = f"📁 {share_name}"
                        self.right_tree.item(child, text=new_text)
                break
    
    def connect_all_mac_shares(self):
        """Try to connect to all Mac shares"""
        shares = ["Home Theater", "Macintosh HD", "Michael Nichols's Public Folder", "michaelnichols", "Theater 3"]
        
        self.status_text.set("Connecting to all Mac shares...")
        connected_count = 0
        
        def connect_share_thread(share_name):
            nonlocal connected_count
            try:
                # Try connecting in background
                server = "**************"
                username = "Michael Nichols"
                password = "jsthogn"
                
                gvfs_path = f"/run/user/{os.getuid()}/gvfs/smb-share:server={server},share={urllib.parse.quote(share_name)}"
                
                if os.path.exists(gvfs_path):
                    connected_count += 1
                    return True
                
                # Try connection
                share_url = f"smb://Michael%20Nichols:{password}@{server}/{urllib.parse.quote(share_name)}"
                result = subprocess.run(['gio', 'mount', share_url], 
                                      capture_output=True, text=True, timeout=15)
                
                time.sleep(1)
                if os.path.exists(gvfs_path):
                    connected_count += 1
                    return True
                    
            except Exception as e:
                print(f"Failed to connect to {share_name}: {e}")
            return False
        
        # Try connecting to all shares
        import threading
        threads = []
        for share in shares:
            thread = threading.Thread(target=connect_share_thread, args=(share,), daemon=True)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=20)
        
        # Update status
        if connected_count > 0:
            self.status_text.set(f"✅ Connected to {connected_count}/{len(shares)} Mac shares")
            self.root.after(1000, self.check_mounted_shares)
        else:
            self.status_text.set("⚠️ No automatic connections - try manual connection")
            messagebox.showinfo("Connection Help", 
                "Automatic connection failed for all shares.\n\n"
                "Try clicking individual share buttons, or:\n\n"
                "1. Open file manager (Nautilus)\n"
                "2. Go to 'Other Locations'\n"
                "3. Connect to: smb://**************\n"
                "4. Enter credentials when prompted\n\n"
                "After manual connection, click 'Check Mounts'")

    def setup_bottom_panel(self, parent):
        bottom_frame = ttk.Frame(parent)
        bottom_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Transfer status frame
        status_frame = ttk.LabelFrame(bottom_frame, text="Transfer Status", padding="5")
        status_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Progress bar with percentage
        progress_frame = ttk.Frame(status_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 5))
        
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.transfer_progress, maximum=100)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        self.progress_label = ttk.Label(progress_frame, text="0%", width=8)
        self.progress_label.pack(side=tk.RIGHT)
        
        # Status details
        details_frame = ttk.Frame(status_frame)
        details_frame.pack(fill=tk.X)
        
        # Current action
        ttk.Label(details_frame, text="Action:", font=("Arial", 9, "bold")).grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.action_label = ttk.Label(details_frame, textvariable=self.status_text, font=("Arial", 9))
        self.action_label.grid(row=0, column=1, sticky=tk.W)
        
        # Transfer speed and time
        self.speed_text = tk.StringVar(value="Speed: --")
        self.time_text = tk.StringVar(value="Time: --")
        
        ttk.Label(details_frame, text="Speed:", font=("Arial", 9, "bold")).grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Label(details_frame, textvariable=self.speed_text, font=("Arial", 9)).grid(row=1, column=1, sticky=tk.W)
        
        ttk.Label(details_frame, text="Elapsed:", font=("Arial", 9, "bold")).grid(row=1, column=2, sticky=tk.W, padx=(20, 5))
        ttk.Label(details_frame, textvariable=self.time_text, font=("Arial", 9)).grid(row=1, column=3, sticky=tk.W)
        
        # File details for folder transfers
        self.file_details_text = tk.StringVar(value="")
        self.file_details_label = ttk.Label(details_frame, textvariable=self.file_details_text, font=("Arial", 8), foreground="gray")
        self.file_details_label.grid(row=2, column=0, columnspan=4, sticky=tk.W, pady=(5, 0))
        
    def populate_tree(self, tree, path):
        """Populate a tree view with directory contents - optimized with pagination
        
        Handles both local directories and network shares, showing them in a hierarchical view.
        """
        try:
            # Check if we need to refresh (avoid unnecessary refreshes)
            if hasattr(tree, '_last_path') and tree._last_path == path and not getattr(tree, '_force_refresh', False):
                return
                
            tree._last_path = path
            tree._force_refresh = False
            
            # Clear the tree
            tree.delete(*tree.get_children())
            
            # Show loading indicator
            loading_item = tree.insert("", "end", text="⏳ Loading...", values=("", ""))
            self.root.update_idletasks()
            
            # Handle network path
            if path == "Network":
                tree.delete(loading_item)
                self.setup_mac_shares_right_pane()
                return
                
            # Check path accessibility
            if not os.path.exists(path) or not os.access(path, os.R_OK):
                tree.delete(loading_item)
                tree.insert("", "end", text="❌ Access Denied or Path Not Found", values=("", ""))
                return
                
            items = []
            
            try:
                # Get all directory items with timeout for network paths
                import signal
                
                def timeout_handler(signum, frame):
                    raise TimeoutError("Directory listing timeout")
                
                dir_items = os.listdir(path)
                
                # Cancel timeout
                if path.startswith("/run/user") or "gvfs" in path:
                    signal.alarm(0)
                total_items = len(dir_items)
                
                # Initialize pagination state if not exists
                if not hasattr(tree, '_pagination_offset'):
                    tree._pagination_offset = 0
                
                # Handle large directories with pagination
                if total_items > self.max_items_before_pagination:
                    items_per_page = self.items_per_page
                    current_offset = getattr(tree, '_pagination_offset', 0)
                    
                    # Add pagination info at the top
                    current_page = (current_offset // items_per_page) + 1
                    total_pages = (total_items + items_per_page - 1) // items_per_page
                    
                    tree.insert("", "end", text=f"📊 Showing page {current_page} of {total_pages} ({total_items} total items)", 
                              values=("", ""), tags=("info",))
                    tree.insert("", "end", text="", values=("", ""))  # Spacer
                    
                    # Add navigation buttons if not on first page
                    if current_offset > 0:
                        tree.insert("", "end", text="⬅️ Previous Page", values=("", ""), tags=("nav_prev",))
                    
                    # Add next page button if there are more items
                    if current_offset + items_per_page < total_items:
                        tree.insert("", "end", text="➡️ Next Page", values=("", ""), tags=("nav_next",))
                    
                    if current_offset > 0 or current_offset + items_per_page < total_items:
                        tree.insert("", "end", text="", values=("", ""))  # Spacer
                    
                    # Get items for current page
                    dir_items = dir_items[current_offset:current_offset + items_per_page]
                else:
                    # Reset pagination for small directories
                    tree._pagination_offset = 0
                
                processed_count = 0
                for item in dir_items:
                    if item.startswith('.') and item not in ['.', '..']:
                        continue  # Skip most hidden files
                        
                    item_path = os.path.join(path, item)
                    
                    try:
                        stat = os.stat(item_path)
                        
                        if os.path.isdir(item_path):
                            icon = "📁"
                            size = ""
                            item_type = "folder"
                        else:
                            icon = "📄"
                            size = self._format_size(stat.st_size)
                            item_type = "file"
                            
                        # Simplified date format for performance
                        modified = time.strftime("%m/%d %H:%M", time.localtime(stat.st_mtime))
                        items.append((f"{icon} {item}", size, modified, item_type, item))
                        
                        processed_count += 1
                        # Update loading indicator periodically for large directories
                        if processed_count % 100 == 0:
                            tree.item(loading_item, text=f"⏳ Loading... ({processed_count}/{total_items})")
                            self.root.update_idletasks()
                        
                    except (OSError, PermissionError):
                        continue
                        
                # Sort: folders first, then files (limit sorting overhead)
                items.sort(key=lambda x: (x[3] != "folder", x[0].lower()))
                
                # Remove loading indicator
                tree.delete(loading_item)
                
                # Batch insert for better performance
                for display_name, size, modified, item_type, real_name in items:
                    tree.insert("", "end", text=display_name, values=(size, modified), tags=(item_type, real_name))
                    
            except PermissionError:
                tree.delete(loading_item)
                tree.insert("", "end", text="❌ Permission Denied", values=("", ""))
            except TimeoutError:
                tree.delete(loading_item)
                tree.insert("", "end", text="❌ Network Timeout", values=("", ""))
                
        except Exception as e:
            # Make sure loading indicator is removed
            try:
                tree.delete(loading_item)
            except:
                pass
            tree.insert("", "end", text=f"❌ Error: {str(e)}", values=("", ""))
            
    def _format_size(self, size_bytes):
        """Format file size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
        
    def _format_speed(self, bytes_per_sec):
        """Format transfer speed in human readable format"""
        for unit in ['B/s', 'KB/s', 'MB/s', 'GB/s']:
            if bytes_per_sec < 1024.0:
                return f"{bytes_per_sec:.1f} {unit}"
            bytes_per_sec /= 1024.0
        return f"{bytes_per_sec:.1f} TB/s"
        
    def _format_time(self, seconds):
        """Format time in human readable format"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds//60:.0f}m {seconds%60:.0f}s"
        else:
            return f"{seconds//3600:.0f}h {(seconds%3600)//60:.0f}m"
            
    def _update_transfer_status(self, progress_percent, current_bytes=0, total_bytes=0, current_file="", force_update=False):
        """Update all transfer status indicators with proper threading"""
        # Throttle updates to prevent GUI overload, but allow forced updates (like completion)
        current_time = time.time()
        if not force_update and hasattr(self, '_last_update_time') and (current_time - self._last_update_time) < 0.2:
            return  # Skip update if less than 200ms since last update
        self._last_update_time = current_time
        
        # Use thread-safe GUI updates
        try:
            self.root.after(0, lambda: self.transfer_progress.set(progress_percent))
            # Format percentage - show 100% when complete, otherwise 1 decimal place
            if progress_percent >= 100:
                self.root.after(0, lambda: self.progress_label.config(text="100%"))
            else:
                self.root.after(0, lambda: self.progress_label.config(text=f"{progress_percent:.1f}%"))
            
            # Calculate and update speed and time
            if self.transfer_start_time:
                elapsed_time = time.time() - self.transfer_start_time
                if elapsed_time > 0 and current_bytes > 0:
                    speed = current_bytes / elapsed_time
                    self.root.after(0, lambda: self.speed_text.set(self._format_speed(speed)))
                else:
                    self.root.after(0, lambda: self.speed_text.set("Calculating..."))
                    
                self.root.after(0, lambda: self.time_text.set(self._format_time(elapsed_time)))
            
            # Update file details for folder transfers
            if self.total_file_count > 1:
                details = f"File {self.current_file_count}/{self.total_file_count}"
                if current_file:
                    details += f" - {current_file}"
                if total_bytes > 0:
                    details += f" ({self._format_size(current_bytes)} / {self._format_size(total_bytes)})"
                self.root.after(0, lambda d=details: self.file_details_text.set(d))
            else:
                if total_bytes > 0:
                    details = f"{self._format_size(current_bytes)} / {self._format_size(total_bytes)}"
                    self.root.after(0, lambda d=details: self.file_details_text.set(d))
        except Exception as e:
            print(f"GUI update error: {e}")
                
    def _reset_transfer_status(self):
        """Reset transfer status to ready state - optimized"""
        self.transfer_start_time = None
        self.bytes_transferred = 0
        self.total_bytes = 0
        self.current_file_count = 0
        self.total_file_count = 0
        if hasattr(self, '_last_update_time'):
            delattr(self, '_last_update_time')
        
        # Batch all GUI updates together
        def reset_gui():
            # Keep the progress at 100% briefly, then reset
            self.root.after(1000, lambda: self.transfer_progress.set(0))
            self.root.after(1000, lambda: self.progress_label.config(text="0%"))
            self.speed_text.set("Speed: --")
            self.time_text.set("Time: --")
            self.file_details_text.set("")
            self.root.after(2000, lambda: self.status_text.set("Ready"))
        
        self.root.after_idle(reset_gui)
        
    def refresh_left_pane(self):
        path = self.left_path.get()
        if path == "Network":
            self.populate_network_browser_left()
        else:
            self.populate_tree(self.left_tree, path)
        
    def refresh_right_pane(self):
        path = self.right_path.get()
        if path == "Network" or path == "Network Shares":
            self.setup_mac_shares_right_pane()
        else:
            self.populate_tree(self.right_tree, path)
        
    def refresh_both_panes(self):
        """Refresh both panes to show current connection status"""
        # Clear cache to force refresh
        if hasattr(self.left_tree, '_last_path'):
            delattr(self.left_tree, '_last_path')
        if hasattr(self.right_tree, '_last_path'):
            delattr(self.right_tree, '_last_path')
        # Refresh left pane
        if self.left_path.get() == "Network":
            self.populate_network_browser_left()
        else:
            self.refresh_left_pane()
        # Refresh right pane - handle both "Network" and "Network Shares"
        right_path = self.right_path.get()
        if right_path == "Network" or right_path == "Network Shares":
            self.setup_mac_shares_right_pane()
        else:
            self.refresh_right_pane()

        # Update status to show current connection count
        connected_shares = self.get_connected_shares()
        connected_count = sum(len(shares) for shares in connected_shares.values())
        if connected_count > 0:
            self.status_text.set(f"✅ {connected_count} shares connected - ready for file transfer")
        else:
            self.status_text.set("No connected shares - click shares to connect")
        
    def on_left_double_click(self, event):
        self._handle_double_click(self.left_tree, self.left_path, self.refresh_left_pane)
        
    def on_right_double_click(self, event):
        self._handle_double_click(self.right_tree, self.right_path, self.refresh_right_pane)
        
    def _parse_server_share_from_path(self, gvfs_path):
        # Example: /run/user/1000/gvfs/smb-share:server=192.168.68.97,share=PopzMint
        import re
        m = re.search(r'server=([^,]+),share=([^/]+)', gvfs_path)
        if m:
            server = m.group(1)
            share = m.group(2)
            return server, share
        return (None, None)

    def _handle_double_click(self, tree, path_var, refresh_func):
        """Handle double-click events on tree items"""
        item = tree.focus()
        if not item:
            return
            
        item_text = tree.item(item, 'text')
        item_values = tree.item(item, 'values')
        tags = tree.item(item, 'tags')
        
        # Handle refresh on double-clicking the parent directory
        if item_text == '..':
            tree._force_refresh = True
            refresh_func()
            return
            
        # Handle network-related items
        if not tags:
            return
            
        # Handle network root node
        if 'network' in tags or 'network_section' in tags:
            if tree == self.left_tree:
                self.left_path.set("Network")
                self.populate_network_browser_left()
            elif tree == self.right_tree:
                self.right_path.set("Network")
                self.setup_mac_shares_right_pane()
            return

        # Handle connected shares section
        if 'connected_section' in tags:
            # Toggle the expanded state
            tree.item(item, open=not tree.item(item, 'open'))
            return

        # Handle connected server nodes
        if 'connected_server' in tags:
            # Toggle the expanded state
            tree.item(item, open=not tree.item(item, 'open'))
            return

        # Handle connected share nodes (direct access)
        if 'connected_share' in tags and len(tags) >= 3:
            mount_point = tags[1]  # The mount point is the second tag
            if os.path.exists(mount_point) and os.access(mount_point, os.R_OK):
                path_var.set(mount_point)
                refresh_func()
                self.status_text.set(f"Navigated to connected share: {tags[2]}")
            else:
                self.status_text.set("Connected share is no longer accessible")
                # Refresh to update the view
                if tree == self.left_tree:
                    self.populate_network_browser_left()
                else:
                    self.setup_mac_shares_right_pane()
            return

        # Handle computer nodes
        if 'computer' in tags:
            # Toggle the expanded state
            tree.item(item, open=not tree.item(item, 'open'))
            return
            
        # Handle share nodes
        if 'share' in tags and len(tags) >= 3:
            share_path = tags[1]  # The SMB URL is the second tag
            
            # Check if this is a connected share
            is_connected = 'connected' in tags
            
            if is_connected:
                # If it's already connected, navigate to it
                gvfs_path = f"/run/user/{os.getuid()}/gvfs/smb-share:server={share_path.split('/')[2]},share={urllib.parse.quote(share_path.split('/')[3])}"
                if os.path.exists(gvfs_path):
                    path_var.set(gvfs_path)
                    refresh_func()
                    return
            
            # If not connected or path not found, try to mount it
            try:
                self.status_text.set(f"Mounting {share_path}...")
                self.root.update_idletasks()
                
                # Try to mount the share
                result = subprocess.run(
                    ['gio', 'mount', share_path],
                    capture_output=True,
                    text=True,
                    timeout=15
                )
                
                if result.returncode == 0 or "already mounted" in result.stderr.lower():
                    # Wait a moment for the mount to complete
                    time.sleep(1)
                    gvfs_path = f"/run/user/{os.getuid()}/gvfs/smb-share:server={share_path.split('/')[2]},share={urllib.parse.quote(share_path.split('/')[3])}"
                    if os.path.exists(gvfs_path):
                        path_var.set(gvfs_path)
                        refresh_func()
                        self.status_text.set(f"✅ Mounted and connected to {share_path}")
                        # Refresh both panes to show the new connection
                        self.root.after(500, self.refresh_both_panes)
                    else:
                        self.status_text.set(f"Mounted but path not found: {gvfs_path}")
                else:
                    self.status_text.set(f"Failed to mount: {result.stderr}")
                    messagebox.showerror("Mount Error", f"Could not mount {share_path}\n\n{result.stderr}")

            except Exception as e:
                error_msg = str(e)
                self.status_text.set(f"Error: {error_msg}")
                messagebox.showerror("Error", f"Could not mount share: {error_msg}")

            # Refresh both panes to update connection status
            self.root.after(1000, self.refresh_both_panes)
                
    def populate_network_browser(self):
        """Populate the right tree with all discovered SMB hosts. Shares will be listed on expand/click."""
        import subprocess
        self.right_tree.delete(*self.right_tree.get_children())
        hosts = []
        try:
            scan_output = subprocess.check_output(["python3", "lan_hosts_discovery.py"], cwd=os.path.dirname(__file__), text=True, timeout=60)
            for line in scan_output.splitlines():
                line = line.strip()
                if line and not line.startswith("#"):
                    hosts.append(line)
        except Exception as e:
            self.right_tree.insert("", "end", text=f"❌ Host scan failed: {e}", values=("", ""))
        for host in hosts:
            node = self.right_tree.insert("", "end", text=f"🖥️ {host}", values=("", ""), tags=("computer", host))
            # Add dummy child for expansion arrow
            self.right_tree.insert(node, "end", text="(expand to list shares)", values=("", ""), tags=("dummy",))
        if not hosts:
            self.right_tree.insert("", "end", text="No network computers found.", values=("", ""))
        self.right_path.set("Network")
    
    def connect_popzvps(self):
        """Connect to POPZVPS - same as file manager"""
        try:
            server = "*************"
            share = "michael5cents-home"
            gvfs_path = f"/run/user/{os.getuid()}/gvfs/smb-share:server={server},share={share}"
            
            # Check if already connected
            if os.path.exists(gvfs_path) and os.access(gvfs_path, os.R_OK):
                self.status_text.set("✅ POPZVPS already connected")
                self.go_to_path_right(gvfs_path)
                return
            
            # Just use gio mount - same as file manager
            self.status_text.set("Connecting to POPZVPS...")
            result = subprocess.run(['gio', 'mount', f'smb://{server}/{share}'], 
                                  capture_output=True, text=True)
            
            # Check if it worked
            time.sleep(2)
            if os.path.exists(gvfs_path) and os.access(gvfs_path, os.R_OK):
                self.status_text.set("✅ Connected to POPZVPS")
                self.go_to_path_right(gvfs_path) 
                threading.Thread(target=self._test_popzvps_connection, daemon=True).start()
            else:
                # Connection needs credentials - same message file manager would show
                self.status_text.set("⚠️ Please enter credentials in the authentication dialog")
                
        except Exception as e:
            self.status_text.set("❌ Connection error")
            print(f"Error: {e}")
    
    def _setup_python_smb_bridge(self):
        """Setup Python SMB client as bridge"""
        try:
            # Create a temporary directory structure that mirrors SMB access
            import tempfile
            bridge_dir = tempfile.mkdtemp(prefix='popzvps_bridge_')
            
            # Create a simple bridge script
            bridge_script = f'''#!/usr/bin/env python3
import os, sys, subprocess
# Simple SMB bridge using smbclient
def list_files(path=""):
    result = subprocess.run([
        'smbclient', '//*************/michael5cents-home', '5904',
        '-U', 'michael5cents', '-c', f'ls {path}'
    ], capture_output=True, text=True)
    return result.stdout if result.returncode == 0 else ""

def get_file(remote_path, local_path):
    result = subprocess.run([
        'smbclient', '//*************/michael5cents-home', '5904',
        '-U', 'michael5cents', '-c', f'get "{remote_path}" "{local_path}"'
    ], capture_output=True, text=True)
    return result.returncode == 0

# Create bridge directory structure
os.makedirs('{bridge_dir}/bridge_info', exist_ok=True)
with open('{bridge_dir}/bridge_info/connection.txt', 'w') as f:
    f.write("POPZVPS SMB Bridge Active\\nServer: *************\\nShare: michael5cents-home\\n")

print("SMB Bridge created at: {bridge_dir}")
'''
            
            with open(f'{bridge_dir}/smb_bridge.py', 'w') as f:
                f.write(bridge_script)
            
            # Execute the bridge setup
            exec(bridge_script)
            
            self.status_text.set("✅ Connected via Python SMB bridge")
            self.go_to_path_right(bridge_dir)
            messagebox.showinfo("SMB Bridge Active", 
                f"Connected using Python SMB bridge.\n\n"
                f"Location: {bridge_dir}\n\n"
                f"Note: This is a fallback connection method.\n"
                f"File operations will use smbclient commands.")
                
        except Exception as e:
            self.status_text.set("❌ SMB bridge setup failed")
            print(f"SMB bridge error: {e}")
    
    def _try_alternative_mount(self):
        """Try alternative mounting method"""
        try:
            self.status_text.set("Trying alternative mount method...")
            
            # Create a credentials file temporarily
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.creds', delete=False) as cred_file:
                cred_file.write("username=michael5cents\n")
                cred_file.write("password=5904\n")
                cred_file.write("domain=WORKGROUP\n")
                cred_file_path = cred_file.name
            
            # Try using mount.cifs if available
            try:
                mount_point = "/tmp/popzvps_mount"
                os.makedirs(mount_point, exist_ok=True)
                
                mount_result = subprocess.run([
                    'sudo', 'mount', '-t', 'cifs',
                    '//*************/michael5cents-home',
                    mount_point,
                    '-o', f'credentials={cred_file_path},uid={os.getuid()},gid={os.getgid()}'
                ], capture_output=True, text=True, timeout=15)
                
                if mount_result.returncode == 0:
                    self.status_text.set("✅ Connected via alternative method")
                    self.go_to_path_right(mount_point)
                else:
                    self.status_text.set("❌ All mount methods failed")
                    messagebox.showinfo("Connection Issue", 
                        "Could not automatically mount POPZVPS.\n\n"
                        "You can manually connect by:\n"
                        "1. Opening file manager\n"
                        "2. Going to 'Other Locations'\n"
                        "3. Connecting to: smb://*************/michael5cents-home\n"
                        "4. Using credentials: michael5cents / 5904")
                        
            except subprocess.CalledProcessError:
                self.status_text.set("❌ Alternative mount failed")
                
            # Clean up credentials file
            try:
                os.unlink(cred_file_path)
            except:
                pass
                
        except Exception as e:
            self.status_text.set("❌ Alternative mount error")
            print(f"Alternative mount error: {e}")
    
    def _open_file_manager_connection(self):
        """Open file manager to establish SMB connection"""
        try:
            # Open file manager with the SMB URL
            result = subprocess.run([
                'nautilus', 
                'smb://*************/michael5cents-home'
            ], capture_output=True, text=True)
            
            # Show instructions to user
            messagebox.showinfo("Manual Connection Required", 
                "File manager opened for POPZVPS connection.\n\n"
                "When prompted, enter:\n"
                "• Username: michael5cents\n"
                "• Password: 5904\n\n"
                "After connecting successfully, close this dialog\n"
                "and click 'Connect POPZVPS' again.")
            
            # Wait a moment and check if connection was established
            time.sleep(2)
            gvfs_path = "/run/user/1000/gvfs/smb-share:server=*************,share=michael5cents-home"
            if os.path.exists(gvfs_path):
                self.status_text.set("✅ Connection established via file manager")
                self.go_to_path_right(gvfs_path)
                threading.Thread(target=self._test_popzvps_connection, daemon=True).start()
            else:
                self.status_text.set("⚠️ Please connect manually in file manager")
                
        except FileNotFoundError:
            # Try with different file managers
            for fm in ['thunar', 'dolphin', 'pcmanfm', 'xdg-open']:
                try:
                    subprocess.run([fm, 'smb://*************/michael5cents-home'], 
                                 capture_output=True, text=True)
                    break
                except FileNotFoundError:
                    continue
            else:
                # No file manager found, show manual instructions
                messagebox.showinfo("Manual Connection Instructions", 
                    "Please manually connect to POPZVPS:\n\n"
                    "1. Open your file manager\n"
                    "2. Go to 'Other Locations' or press Ctrl+L\n"
                    "3. Type: smb://*************/michael5cents-home\n"
                    "4. Enter username: michael5cents\n"
                    "5. Enter password: 5904\n\n"
                    "After connecting, click 'Connect POPZVPS' again.")
                    
        except Exception as e:
            self.status_text.set("❌ File manager connection failed")
            print(f"File manager error: {e}")
    
    def _try_interactive_popzvps_auth(self):
        """Try interactive authentication for POPZVPS using smbclient"""
        try:
            # Alternative method using smbclient for authentication
            self.status_text.set("Attempting alternative connection method...")
            
            # Test connection with smbclient first
            smbclient_cmd = [
                'smbclient', 
                '//*************/michael5cents-home', 
                '5904',  # password
                '-U', 'michael5cents',  # username
                '-c', 'ls'  # just list files to test
            ]
            
            result = subprocess.run(smbclient_cmd, 
                                  capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                # smbclient worked, now try mounting without password in URL
                mount_result = subprocess.run(
                    ['gio', 'mount', 'smb://*************/michael5cents-home'],
                    input=f"{username}\n{password}\n",
                    capture_output=True, text=True, timeout=20
                )
                
                if mount_result.returncode == 0:
                    self.status_text.set("✅ Connected to POPZVPS (alternative method)")
                    gvfs_path = "/run/user/1000/gvfs/smb-share:server=*************,share=michael5cents-home"
                    self.go_to_path_right(gvfs_path)
                    threading.Thread(target=self._test_popzvps_connection, daemon=True).start()
                else:
                    self.status_text.set("❌ Mount failed after successful authentication")
            else:
                error_msg = result.stderr.strip() if result.stderr else result.stdout.strip()
                self.status_text.set("❌ Authentication failed - check credentials")
                messagebox.showerror("Authentication Failed", 
                    f"Could not authenticate with POPZVPS:\n\n{error_msg}\n\n"
                    "Please verify:\n"
                    "• Username: michael5cents\n"
                    "• Password: 5904\n"
                    "• SMB service is running on *************")
                
        except subprocess.TimeoutExpired:
            self.status_text.set("⏱️ Authentication timed out")
        except FileNotFoundError:
            self.status_text.set("❌ smbclient not found - using basic connection")
            # Fall back to basic gio mount without credentials
            try:
                result = subprocess.run(['gio', 'mount', 'smb://*************/michael5cents-home'], 
                                      capture_output=True, text=True, timeout=15)
                if result.returncode == 0:
                    self.status_text.set("✅ Connected to POPZVPS (may need manual auth)")
            except:
                self.status_text.set("❌ All connection methods failed")
        except Exception as e:
            self.status_text.set(f"❌ Alternative connection failed: {str(e)}")
            print(f"Interactive auth error: {e}")
    
    def _test_popzvps_connection(self):
        """Test POPZVPS connection by attempting to list files"""
        try:
            gvfs_path = "/run/user/1000/gvfs/smb-share:server=*************,share=michael5cents-home"
            
            # Wait a moment for connection to stabilize
            time.sleep(2)
            
            if os.path.exists(gvfs_path):
                # Try to list directory contents to verify working connection
                test_list = os.listdir(gvfs_path)
                file_count = len([f for f in test_list if os.path.isfile(os.path.join(gvfs_path, f))])
                folder_count = len([f for f in test_list if os.path.isdir(os.path.join(gvfs_path, f))])
                
                self.root.after(0, lambda: self.status_text.set(
                    f"✅ POPZVPS connected - {file_count} files, {folder_count} folders"))
                
                # Refresh the right pane to show contents
                self.root.after(1000, self.refresh_right_pane)
            else:
                self.root.after(0, lambda: self.status_text.set("❌ POPZVPS mount point not found"))
                
        except Exception as e:
            self.root.after(0, lambda: self.status_text.set("⚠️ POPZVPS connected but may have issues"))
            print(f"POPZVPS test error: {e}")
    
    def connect_all_shares(self):
        """Connect to all known shares from both machines"""
        try:
            self.status_text.set("Connecting to all shares...")
            
            # Define all known shares with priorities
            shares_to_connect = [
                ("*************", "michael5cents-home", "POPZVPS"),
                ("**************", "Theater", "Home Theater"),
                ("**************", "theater3", "Theater 3"),
                ("**************", "public", "Public"),
                ("michaels-mac-pro-game-room.local", "music etc", "Music Etc"),
                ("michaels-mac-pro-game-room.local", "plex drive", "Plex Drive"),
                ("michaels-mac-pro-game-room.local", "michaelnichols", "Michael N"),
            ]
            
            connected_count = 0
            total_shares = len(shares_to_connect)
            
            # Update status first
            self.root.update_idletasks()
            
            for i, (server, share_name, display_name) in enumerate(shares_to_connect):
                try:
                    # Update progress
                    progress = i / total_shares * 100
                    self.status_text.set(f"Connecting... {i+1}/{total_shares} ({display_name})")
                    self.root.update_idletasks()
                    
                    # Use URL encoding for share names with spaces
                    if server == "*************":
                        # POPZVPS requires authentication
                        share_url = f"smb://michael5cents:5904@{server}/{share_name.replace(' ', '%20')}"
                    elif server == "**************" or "theater" in server.lower():
                        # Home Theater requires workgroup authentication
                        # For gio mount, we need to encode the username with workgroup
                        theater_auth_username = f"WORKGROUP%5C{urllib.parse.quote(theater_username)}"
                        share_url = f"smb://{theater_auth_username}:jsthogn@{server}/{share_name.replace(' ', '%20')}"
                    else:
                        share_url = f"smb://{server}/{share_name.replace(' ', '%20')}"
                    
                    # Try to mount the share with longer timeout for network connections
                    result = subprocess.run(['gio', 'mount', share_url], 
                                          capture_output=True, text=True, timeout=20)
                    
                    if result.returncode == 0:
                        connected_count += 1
                        print(f"✅ Connected to {server}/{share_name} ({display_name})")
                    elif "already mounted" in result.stderr.lower():
                        connected_count += 1
                        print(f"✅ Already connected to {server}/{share_name} ({display_name})")
                    else:
                        print(f"❌ Failed to connect to {server}/{share_name} ({display_name}): {result.stderr}")
                        
                except subprocess.TimeoutExpired:
                    print(f"⏱️ Timeout connecting to {server}/{share_name} ({display_name})")
                    # Continue trying other shares
                except Exception as e:
                    print(f"❌ Error connecting to {server}/{share_name}: {e}")
                    
            self.status_text.set(f"Connected to {connected_count}/{total_shares} shares")
            
            # Refresh the network browser to show newly connected shares
            self.root.after(1000, self.populate_network_browser)
            
            # Show summary message
            if connected_count == 0:
                messagebox.showinfo("Connection Summary", 
                    "No shares were connected successfully.\n\n"
                    "Try connecting individually using:\n"
                    "• 'Browse Network' to discover computers\n"
                    "• Quick access buttons for known shares")
            elif connected_count == total_shares:
                messagebox.showinfo("Connection Summary", 
                    f"✅ All {connected_count} shares connected successfully!")
            else:
                messagebox.showinfo("Connection Summary", 
                    f"Connected to {connected_count} out of {total_shares} shares.\n\n"
                    "Some shares may require manual connection or different credentials.")
            
        except Exception as e:
            print(f"Error connecting to shares: {e}")
            self.status_text.set("Error connecting to shares")
            messagebox.showerror("Connection Error", f"An error occurred while connecting to shares:\n{str(e)}")
                
    def _handle_page_key(self, tree, refresh_func, direction):
        """Handle Page Up/Page Down keys for pagination navigation"""
        current_offset = getattr(tree, '_pagination_offset', 0)
        
        if direction == "prev" and current_offset > 0:
            tree._pagination_offset = max(0, current_offset - self.items_per_page)
            tree._force_refresh = True
            refresh_func()
        elif direction == "next":
            tree._pagination_offset = current_offset + self.items_per_page
            tree._force_refresh = True
            refresh_func()
                
    def go_up_left(self):
        current = self.left_path.get()
        parent = os.path.dirname(current)
        if parent != current:
            self.left_path.set(parent)
            self.refresh_left_pane()
            
    def go_up_right(self):
        current = self.right_path.get()
        parent = os.path.dirname(current)
        if parent != current:
            self.right_path.set(parent)
            self.refresh_right_pane()
            
    def go_home_left(self):
        self.left_path.set(os.path.expanduser("~"))
        self.refresh_left_pane()
        
    def go_home_right(self):
        self.right_path.set("Network Shares")
        self.setup_mac_shares_right_pane()
        
    def go_to_path_left(self, path):
        self.left_path.set(path)
        self.refresh_left_pane()
        
    def go_to_path_right(self, path):
        """Navigate to the specified path in the right pane, handling both local and network paths"""
        if path.startswith("smb://"):
            # Handle SMB network share path
            try:
                # Parse the SMB URL
                from urllib.parse import urlparse
                parsed = urlparse(path)
                server = parsed.netloc.split('@')[-1]  # Handle credentials if present
                share_path = parsed.path.lstrip('/')
                
                # Convert to GVFS mount path
                gvfs_path = f"/run/user/{os.getuid()}/gvfs/smb-share:server={server},share={urllib.parse.quote(share_path)}"
                
                # Check if the share is already mounted
                if not os.path.exists(gvfs_path):
                    # If not mounted, try to mount it
                    self.status_text.set(f"Mounting {server}/{share_path}...")
                    self.root.update_idletasks()
                    
                    # Try to mount the share (you may need to add authentication)
                    try:
                        subprocess.run(
                            ['gio', 'mount', path],
                            check=True,
                            timeout=30,
                            capture_output=True,
                            text=True
                        )
                        # Wait a moment for the mount to complete
                        time.sleep(1)
                    except subprocess.CalledProcessError as e:
                        if "already mounted" in e.stderr.lower():
                            # Share is already mounted, continue
                            pass
                        else:
                            raise
                
                # Set the path to the mounted share
                self.right_path.set(gvfs_path)
                self.refresh_right_pane()
                
            except Exception as e:
                messagebox.showerror("Error", f"Could not access network share: {e}")
                self.status_text.set("Error accessing network share")
        else:
            # Handle local paths
            if os.path.exists(path):
                self.right_path.set(path)
                self.refresh_right_pane()
            else:
                messagebox.showerror("Error", f"Path not found: {path}")
                self.status_text.set("Path not found")
        
    def create_folder_right(self):
        current_path = self.right_path.get()
        if not os.access(current_path, os.W_OK):
            messagebox.showerror("Error", "Cannot create folder here - no write permission")
            return
            
        folder_name = simpledialog.askstring("New Folder", "Enter folder name:")
        if folder_name:
            try:
                new_folder_path = os.path.join(current_path, folder_name)
                os.makedirs(new_folder_path)
                self.refresh_right_pane()
                self.status_text.set(f"Created folder: {folder_name}")
            except Exception as e:
                messagebox.showerror("Error", f"Could not create folder: {str(e)}")
                
    def get_selected_item_path(self, tree, current_path):
        """Get the full path of selected item in tree"""
        selection = tree.selection()
        if not selection:
            return None
            
        item = selection[0]
        tags = tree.item(item, "tags")
        
        if len(tags) >= 2:
            real_name = tags[1]
            return os.path.join(current_path, real_name)
        return None
        
    def transfer_to_right(self):
        """Transfer selected item from left to right"""
        source_path = self.get_selected_item_path(self.left_tree, self.left_path.get())
        if not source_path:
            messagebox.showwarning("No Selection", "Please select a file or folder to transfer")
            return
            
        destination_path = self.right_path.get()
        if not os.access(destination_path, os.W_OK):
            messagebox.showerror("Error", "Destination is not writable")
            return
            
        self.start_transfer(source_path, destination_path, self.refresh_right_pane)
        
    def transfer_to_left(self):
        """Transfer selected item from right to left"""
        source_path = self.get_selected_item_path(self.right_tree, self.right_path.get())
        if not source_path:
            messagebox.showwarning("No Selection", "Please select a file or folder to transfer")
            return
            
        destination_path = self.left_path.get()
        if not os.access(destination_path, os.W_OK):
            messagebox.showerror("Error", "Destination is not writable")
            return
            
        self.start_transfer(source_path, destination_path, self.refresh_left_pane)
        
    def start_transfer(self, source, destination, refresh_callback):
        """Start the transfer process"""
        if os.path.isdir(source):
            threading.Thread(target=self._transfer_folder_thread, args=(source, destination, refresh_callback), daemon=True).start()
        else:
            threading.Thread(target=self._transfer_file_thread, args=(source, destination, refresh_callback), daemon=True).start()
            
    def _transfer_file_thread(self, source, destination, refresh_callback):
        """Transfer file with optimized chunking"""
        try:
            filename = os.path.basename(source)
            dest_path = os.path.join(destination, filename)
            
            # Check if file already exists
            if os.path.exists(dest_path):
                result = messagebox.askyesno("File Exists", f"File '{filename}' already exists. Overwrite?")
                if not result:
                    self.root.after(0, lambda: self.status_text.set("Transfer cancelled"))
                    return
                    
            file_size = os.path.getsize(source)
            self.total_bytes = file_size
            self.transfer_start_time = time.time()
            
            self.root.after(0, lambda: self.status_text.set(f"Transferring: {filename}"))
            
            # Use larger chunks for better performance
            chunk_size = min(self.chunk_size, file_size) if file_size > 0 else self.chunk_size
            
            with open(source, 'rb') as src, open(dest_path, 'wb') as dst:
                copied = 0
                while True:
                    chunk = src.read(chunk_size)
                    if not chunk:
                        break
                    dst.write(chunk)
                    copied += len(chunk)
                    
                    # Update status less frequently for large files
                    if copied % (chunk_size * 5) == 0 or copied == file_size:
                        progress = (copied / file_size) * 100 if file_size > 0 else 100
                        progress = min(progress, 100.0)
                        self._update_transfer_status(progress, copied, file_size, filename)
                    
            # Transfer complete
            self._update_transfer_status(100, file_size, file_size, filename, force_update=True)
            self.root.after(0, lambda: self.status_text.set(f"✅ Transfer completed: {filename}"))
            self.root.after(0, refresh_callback)
            
            # Reset status after a delay
            self.root.after(3000, self._reset_transfer_status)
            
        except Exception as e:
            error_msg = str(e)
            print(f"File transfer error: {error_msg}")
            try:
                self.root.after(0, lambda: messagebox.showerror("Transfer Error", f"Error transferring file: {error_msg}"))
                self.root.after(0, lambda: self.status_text.set("❌ Transfer failed"))
                self.root.after(2000, self._reset_transfer_status)
            except Exception:
                print("Failed to show error dialog")
            
    def _transfer_folder_thread(self, source_folder, destination, refresh_callback):
        """Transfer folder with optimized parallel processing"""
        try:
            folder_name = os.path.basename(source_folder)
            dest_folder_path = os.path.join(destination, folder_name)
            
            # Fast scan for large directories - collect files first
            self.root.after(0, lambda: self.status_text.set(f"Scanning folder: {folder_name}..."))
            file_list = []
            self.total_file_count = 0
            self.total_bytes = 0
            
            # Use pathlib for faster directory scanning
            source_path = Path(source_folder)
            try:
                for file_path in source_path.rglob('*'):
                    if file_path.is_file():
                        try:
                            file_size = file_path.stat().st_size
                            rel_path = file_path.relative_to(source_path)
                            dest_file = Path(dest_folder_path) / rel_path
                            file_list.append((str(file_path), str(dest_file), file_size))
                            self.total_bytes += file_size
                            self.total_file_count += 1
                            
                            # Update scan progress for very large folders
                            if self.total_file_count % 1000 == 0:
                                self.root.after(0, lambda c=self.total_file_count: 
                                    self.status_text.set(f"Scanning... {c} files found"))
                        except:
                            continue
            except Exception as e:
                print(f"Error scanning directory: {e}")
                return
            
            self.current_file_count = 0
            self.bytes_transferred = 0
            self.transfer_start_time = time.time()
            
            self.root.after(0, lambda: self.status_text.set(f"Transferring folder: {folder_name} ({self.total_file_count} files)"))
            
            # Check if folder already exists
            if os.path.exists(dest_folder_path):
                merge_confirmed = [False]
                dialog_completed = [False]
                
                def show_dialog():
                    result = messagebox.askyesno("Folder Exists", f"Folder '{folder_name}' already exists. Merge contents?")
                    merge_confirmed[0] = result
                    dialog_completed[0] = True
                
                self.root.after(0, show_dialog)
                
                while not dialog_completed[0]:
                    time.sleep(0.1)
                
                if not merge_confirmed[0]:
                    self.root.after(0, lambda: self.status_text.set("Transfer cancelled"))
                    return
            else:
                os.makedirs(dest_folder_path, exist_ok=True)
            
            # Create all directories first
            for src_file, dest_file, _ in file_list:
                dest_dir = os.path.dirname(dest_file)
                if dest_dir and not os.path.exists(dest_dir):
                    try:
                        os.makedirs(dest_dir, exist_ok=True)
                    except Exception as e:
                        print(f"Error creating directory {dest_dir}: {e}")
            
            # Transfer files with parallel processing for better speed
            def copy_file(file_info):
                src_file, dest_file, file_size = file_info
                try:
                    filename = os.path.basename(src_file)
                    
                    # Use optimized chunk size
                    chunk_size = min(self.chunk_size, file_size) if file_size > 0 else self.chunk_size
                    
                    with open(src_file, 'rb') as src, open(dest_file, 'wb') as dst:
                        copied = 0
                        while True:
                            chunk = src.read(chunk_size)
                            if not chunk:
                                break
                            dst.write(chunk)
                            copied += len(chunk)
                            self.bytes_transferred += len(chunk)
                    
                    # Preserve file metadata
                    try:
                        shutil.copystat(src_file, dest_file)
                    except:
                        pass
                        
                    self.current_file_count += 1
                    
                    # Update progress every 10 files for better performance
                    if self.current_file_count % 10 == 0:
                        overall_progress = (self.bytes_transferred / self.total_bytes) * 100 if self.total_bytes > 0 else 0
                        overall_progress = min(overall_progress, 100.0)
                        self._update_transfer_status(overall_progress, self.bytes_transferred, self.total_bytes, filename)
                        
                    return True
                except Exception as e:
                    print(f"Error copying {src_file}: {e}")
                    return False
            
            # Use ThreadPoolExecutor for parallel file copying
            successful_transfers = 0
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = [executor.submit(copy_file, file_info) for file_info in file_list]
                
                for future in concurrent.futures.as_completed(futures):
                    try:
                        if future.result():
                            successful_transfers += 1
                    except Exception as e:
                        print(f"Transfer error: {e}")
                        
            # Transfer complete
            self._update_transfer_status(100, self.total_bytes, self.total_bytes, force_update=True)
            self.root.after(0, lambda: self.status_text.set(f"✅ Folder transfer completed: {folder_name} ({successful_transfers}/{self.total_file_count} files)"))
            self.root.after(0, refresh_callback)
            
            self.root.after(5000, self._reset_transfer_status)
            
        except Exception as e:
            error_msg = str(e)
            print(f"Folder transfer error: {error_msg}")
            try:
                self.root.after(0, lambda: messagebox.showerror("Transfer Error", f"Error transferring folder: {error_msg}"))
                self.root.after(0, lambda: self.status_text.set("❌ Folder transfer failed"))
                self.root.after(3000, self._reset_transfer_status)
            except Exception:
                print("Failed to show error dialog")


if __name__ == "__main__":
    root = tk.Tk()
    app = DualPaneFileTransfer(root)
    root.mainloop()