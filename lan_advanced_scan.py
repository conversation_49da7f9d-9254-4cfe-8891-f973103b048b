#!/usr/bin/env python3
import subprocess
import re
import sys
import socket
import urllib.parse

def discover_hosts_avahi():
    hosts = set()
    try:
        output = subprocess.check_output(["avahi-browse", "-rt", "_smb._tcp"], text=True, timeout=15)
        for line in output.splitlines():
            if 'address = ' in line:
                ip = line.split('address = ')[1].split(',')[0].strip()
                hosts.add(ip)
    except Exception as e:
        print(f"#AVAHI_ERROR {e}", file=sys.stderr)
    return hosts

def discover_hosts_smbtree():
    hosts = set()
    try:
        output = subprocess.check_output(["smbtree", "-N"], text=True, timeout=20)
        for line in output.splitlines():
            m = re.match(r'\\\\([\w\-\.]+)', line.strip())
            if m:
                name = m.group(1)
                # Try to resolve name to IP
                try:
                    ip = socket.gethostbyname(name)
                    hosts.add(ip)
                except Exception:
                    pass
    except Exception as e:
        print(f"#SMBTREE_ERROR {e}", file=sys.stderr)
    return hosts

def list_shares(host):
    shares = []
    errors = []
    for user_args, label in ([['-N'], 'anonymous'], [['-U', 'guest%'], 'guest']):
        try:
            out = subprocess.check_output(["smbclient", "-L", host] + user_args, stderr=subprocess.STDOUT, text=True, timeout=7)
            found_section = False
            for line in out.splitlines():
                if "Sharename" in line and "Type" in line:
                    found_section = True
                    continue
                if found_section:
                    if line.strip() == "" or line.startswith("----"):
                        continue
                    parts = line.split()
                    if len(parts) > 1 and parts[1] == "Disk":
                        shares.append((host, parts[0], label))
            if shares:
                return shares, errors
        except subprocess.CalledProcessError as e:
            errors.append(f"{label} error: {e.output.strip().splitlines()[-1] if e.output else str(e)}")
        except Exception as e:
            errors.append(f"{label} error: {e}")
    return shares, errors

def main():
    all_hosts = set()
    all_hosts.update(discover_hosts_avahi())
    all_hosts.update(discover_hosts_smbtree())
    found = []
    errors = []
    for host in sorted(all_hosts):
        shares, errs = list_shares(host)
        found.extend(shares)
        for err in errs:
            errors.append((host, err))
    print("#FOUND")
    for host, share, method in found:
        print(f"{host} {share} {method}")
    print("#ERRORS")
    for host, msg in errors:
        print(f"{host} {msg}")

if __name__ == "__main__":
    main()
