#!/usr/bin/env python3
import subprocess
import re
import socket
import sys
import json
from collections import defaultdict
import os
import urllib.parse

def get_workgroup():
    """Get the workgroup name using nmblookup"""
    try:
        result = subprocess.run(
            ['nmblookup', '-S', '--broadcast'],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'workgroup:' in line.lower():
                    return line.split(':')[-1].strip()
    except Exception as e:
        print(f"#WORKGROUP_ERROR {e}", file=sys.stderr)
    return 'WORKGROUP'  # Default workgroup name

def discover_computers():
    """Discover computers in the network using findsmb and nmblookup"""
    computers = set()
    workgroup = get_workgroup()
    
    # Try findsmb first
    try:
        result = subprocess.run(
            ['findsmb', '-w', workgroup],
            capture_output=True,
            text=True,
            timeout=15
        )
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if '\\' in line and 'Workgroup' in line:
                    computer = line.split('\\')[-1].split()[0].strip()
                    if computer and computer.upper() != workgroup.upper():
                        computers.add(computer.upper())
    except Exception as e:
        print(f"#FINDSMB_ERROR {e}", file=sys.stderr)
    
    # Fallback to nmblookup if no computers found
    if not computers:
        try:
            result = subprocess.run(
                ['nmblookup', '-S', '--broadcast'],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'Looking up status of' in line:
                        computer = line.split()[-1].strip()
                        if computer and computer.upper() != workgroup.upper():
                            computers.add(computer.upper())
        except Exception as e:
            print(f"#NMBLOOKUP_ERROR {e}", file=sys.stderr)
    
    return sorted(computers)

def get_shares(computer):
    """Get shares from a computer using smbclient"""
    shares = []
    try:
        result = subprocess.run(
            ['smbclient', f'//{computer}', '-N', '-L'],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'Disk' in line and not any(x in line for x in ['IPC$', 'print$', 'ADMIN$', 'C$']):
                    parts = line.split()
                    if parts and not parts[0].startswith('----'):
                        share = parts[0].strip()
                        if share and share not in shares:
                            shares.append(share)
    except Exception as e:
        print(f"#SMBCLIENT_ERROR {e} for {computer}", file=sys.stderr)
    
    return shares

def get_connected_shares():
    """Get list of currently connected SMB shares from gvfs"""
    connected = defaultdict(list)
    try:
        gvfs_base = f"/run/user/{os.getuid()}/gvfs"
        if os.path.exists(gvfs_base):
            for item in os.listdir(gvfs_base):
                if "smb-share:server=" in item:
                    try:
                        # Extract server and share name from the mount point
                        parts = item.split("server=")[1].split(",")
                        server = parts[0]
                        share = urllib.parse.unquote(parts[1].split("share=")[1])
                        
                        # Get mount point
                        mount_point = os.path.join(gvfs_base, item)
                        
                        # Add to connected shares
                        connected[server.upper()].append({
                            'name': share,
                            'mount_point': mount_point,
                            'is_connected': True
                        })
                    except Exception as e:
                        print(f"#GVFS_PARSE_ERROR {e} for {item}", file=sys.stderr)
    except Exception as e:
        print(f"#GVFS_ERROR {e}", file=sys.stderr)
    
    return connected

def main():
    # Output as JSON for better parsing
    output = {
        'workgroup': get_workgroup(),
        'computers': {}
    }
    
    # Get connected shares first
    connected_shares = get_connected_shares()
    
    # Discover all computers
    computers = discover_computers()
    
    for computer in computers:
        shares = get_shares(computer)
        output['computers'][computer] = {
            'shares': [{'name': s, 'is_connected': s in [cs['name'] for cs in connected_shares.get(computer.upper(), [])]} for s in shares],
            'is_online': True
        }
    
    # Add connected computers that might not have been discovered
    for computer, shares in connected_shares.items():
        if computer not in output['computers']:
            output['computers'][computer] = {
                'shares': [{'name': s['name'], 'is_connected': True} for s in shares],
                'is_online': False
            }
    
    print(json.dumps(output, indent=2))

if __name__ == "__main__":
    main()
