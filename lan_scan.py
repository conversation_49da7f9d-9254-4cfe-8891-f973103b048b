#!/usr/bin/env python3
import subprocess
import socket
import ipaddress
import threading
import queue
import re
import sys

def get_local_subnet():
    # Get local IP and subnet mask (Linux)
    ip = None
    mask = None
    try:
        ip_result = subprocess.check_output(["hostname", "-I"], text=True).strip()
        ip = ip_result.split()[0]
        # Use ip command to get mask
        ip_addr = subprocess.check_output(["ip", "addr", "show"], text=True)
        for line in ip_addr.splitlines():
            if ip and ip in line:
                m = re.search(r"inet (\d+\.\d+\.\d+\.\d+)/(\d+)", line)
                if m:
                    mask = int(m.group(2))
                    break
    except Exception as e:
        print(f"Could not determine local subnet: {e}", file=sys.stderr)
        return None, None
    return ip, mask

def scan_host_for_smb(host, outq, errq):
    # Try anonymous first
    for user_args, label in ([["-N"], "anonymous"], [["-U", "guest%"], "guest"]):
        try:
            smb_out = subprocess.check_output(["smbclient", "-L", host] + user_args, stderr=subprocess.STDOUT, text=True, timeout=7)
            shares = []
            found_section = False
            for line in smb_out.splitlines():
                if "Sharename" in line and "Type" in line:
                    found_section = True
                    continue
                if found_section:
                    if line.strip() == "" or line.startswith("----"):
                        continue
                    parts = line.split()
                    if len(parts) > 1 and parts[1] == "Disk":
                        shares.append(parts[0])
            if shares:
                outq.put((host, shares, label))
                return
            elif found_section:
                errq.put((host, f"No accessible shares ({label})"))
        except subprocess.CalledProcessError as e:
            errq.put((host, f"{label} error: {e.output.strip().splitlines()[-1] if e.output else str(e)}"))
        except Exception as e:
            errq.put((host, f"{label} error: {e}"))

def thorough_lan_scan():
    ip, mask = get_local_subnet()
    if not ip or not mask:
        print("Could not determine local subnet.")
        return [], []
    net = ipaddress.IPv4Network(f"{ip}/{mask}", strict=False)
    hosts = [str(h) for h in net.hosts()]
    outq = queue.Queue()
    errq = queue.Queue()
    threads = []
    for host in hosts:
        t = threading.Thread(target=scan_host_for_smb, args=(host, outq, errq))
        t.daemon = True
        t.start()
        threads.append(t)
    for t in threads:
        t.join(timeout=8)
    found = []
    errors = []
    while not outq.empty():
        found.append(outq.get())
    while not errq.empty():
        errors.append(errq.get())
    return found, errors

if __name__ == "__main__":
    found, errors = thorough_lan_scan()
    print("#FOUND")
    for host, shares, method in found:
        for share in shares:
            print(f"{host} {share} {method}")
    print("#ERRORS")
    for host, msg in errors:
        print(f"{host} {msg}")
