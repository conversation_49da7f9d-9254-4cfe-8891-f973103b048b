#!/usr/bin/env python3

import subprocess
import os
import time

def open_manual_connection():
    """Open file manager for manual Mac connection"""
    
    print("🔗 Opening file manager for manual Mac connection...")
    print()
    print("INSTRUCTIONS:")
    print("1. File manager will open to 'Other Locations'")
    print("2. In the 'Connect to Server' box, enter: smb://**************")
    print("3. Click 'Connect'")
    print("4. When prompted for credentials, enter:")
    print("   👤 Username: <PERSON>")
    print("   🔐 Password: jsthogn")
    print("   🏢 Domain: WORKGROUP (if asked)")
    print("5. You'll see all 5 shares available to access")
    print()
    
    try:
        # Open file manager to Other Locations
        subprocess.Popen(['nautilus', 'other-locations:///'], 
                        stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        print("✅ File manager opened!")
        print("📁 After connecting, shares will appear in the LanTransfer app")
        
        # Wait a moment then check for mounted shares
        print("⏳ Waiting 30 seconds then checking for mounted shares...")
        time.sleep(30)
        
        # Check for mounts
        gvfs_base = f"/run/user/{os.getuid()}/gvfs"
        if os.path.exists(gvfs_base):
            mac_mounts = []
            for item in os.listdir(gvfs_base):
                if "**************" in item:
                    mac_mounts.append(item)
            
            if mac_mounts:
                print("🎉 SUCCESS! Found mounted Mac shares:")
                for mount in mac_mounts:
                    share_name = mount.split("share=")[1] if "share=" in mount else "Unknown"
                    print(f"   ✅ {share_name}")
                print("\n📱 These shares are now available in the LanTransfer app!")
            else:
                print("⚠️  No Mac shares detected yet. They may still be connecting...")
                print("   💡 Try running this script again or restart the LanTransfer app")
        
    except Exception as e:
        print(f"❌ Error opening file manager: {e}")
        print("\n🔧 Manual fallback:")
        print("1. Open file manager manually")
        print("2. Press Ctrl+L")
        print("3. Type: smb://**************")
        print("4. Enter the credentials shown above")

if __name__ == "__main__":
    open_manual_connection()