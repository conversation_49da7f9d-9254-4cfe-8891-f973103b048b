#!/usr/bin/env python3
import subprocess
import re
import sys

def scan_smb_shares():
    """
    Scans the local network for SMB/CIFS shares using smbclient.
    Returns a list of (server, share) tuples.
    """
    shares = []
    try:
        # Use nmblookup to find hosts (works on most Linux with Samba installed)
        nmb_output = subprocess.check_output(['nmblookup', '-S', '--', '-'], stderr=subprocess.STDOUT, text=True)
        hosts = set()
        for line in nmb_output.splitlines():
            m = re.match(r'^([0-9.]+) ', line)
            if m:
                hosts.add(m.group(1))
        
        # For each host, list shares
        for host in hosts:
            try:
                smb_output = subprocess.check_output(["smbclient", "-L", host, "-N"], stderr=subprocess.STDOUT, text=True, timeout=5)
                for line in smb_output.splitlines():
                    if "Disk" in line:
                        parts = line.split()
                        if len(parts) > 1:
                            shares.append((host, parts[0]))
            except subprocess.CalledProcessError:
                continue
            except subprocess.TimeoutExpired:
                continue
    except Exception as e:
        print(f"Error scanning SMB shares: {e}", file=sys.stderr)
    return shares

if __name__ == "__main__":
    found = scan_smb_shares()
    for server, share in found:
        print(f"{server} {share}")
