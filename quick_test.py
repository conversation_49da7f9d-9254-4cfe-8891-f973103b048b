#!/usr/bin/env python3
"""
Test quick connection with mi<PERSON><PERSON><PERSON><PERSON><PERSON> username
"""
import subprocess

def test_connection():
    server = "192.168.68.112"
    share = "Theater"
    workgroup = "WORKGROUP"
    username = "michaelnichols"
    password = "jsthogn"
    
    print(f"=== Testing Home Theater Connection ===")
    print(f"Server: {server}")
    print(f"Share: {share}")
    print(f"Username: {username}")
    print(f"Password: {'*' * len(password)}")
    print(f"Workgroup: {workgroup}")
    print()
    
    try:
        # Test share listing first
        auth_string = f"{workgroup}\\{username}%{password}"
        print("Testing share listing...")
        result = subprocess.run([
            "smbclient", "-L", server, "-U", auth_string
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Share listing successful!")
            print("Available shares:")
            lines = result.stdout.splitlines()
            started = False
            for line in lines:
                if "Sharename" in line and "Type" in line:
                    started = True
                    continue
                if started and line.strip() and not line.startswith("-"):
                    if "Disk" in line:
                        share_name = line.split()[0]
                        print(f"  - {share_name}")
                        # Test each share quickly
                        try:
                            test_result = subprocess.run([
                                "smbclient", f"//{server}/{share_name}", "-U", f"{username}%{password}",
                                "-c", "ls"
                            ], capture_output=True, text=True, timeout=5)
                            if test_result.returncode == 0:
                                print(f"    ✅ Accessible: {share_name}")
                                if "home" in share_name.lower() or "theater" in share_name.lower():
                                    print(f"    🎯 RECOMMENDED: Use '{share_name}' as home theater share")
                            else:
                                print(f"    ❌ Not accessible: {share_name}")
                        except:
                            print(f"    ⏱️ Timeout testing: {share_name}")
            return True
            
            # Now test direct access to Theater share
            print(f"Testing direct access to '{share}' share...")
            result2 = subprocess.run([
                "smbclient", f"//{server}/{share}", "-U", f"{username}%{password}",
                "-c", "ls"
            ], capture_output=True, text=True, timeout=10)
            
            if result2.returncode == 0:
                print("✅ Direct access successful!")
                print("Theater share contents:")
                lines = result2.stdout.splitlines()
                for line in lines[10:20]:  # Show first few items
                    if line.strip():
                        print(f"  - {line}")
                return True
            else:
                print(f"❌ Direct access failed with return code: {result2.returncode}")
                print("stdout:", result2.stdout[:200])
                print("stderr:", result2.stderr[:200])
                # Sometimes access succeeds despite warnings, check if we got file listings
                if "blocks of size" in result2.stdout or "NT_STATUS_ACCESS_DENIED" not in result2.stderr:
                    print("⚠️ Access may still have succeeded despite warnings")
                    return True
        else:
            print(f"❌ Share listing failed: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⏱️ Connection timeout")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return False

if __name__ == "__main__":
    success = test_connection()
    print(f"\n=== Result ===")
    if success:
        print("✅ Connection successful! The app should work now.")
    else:
        print("❌ Connection failed. Check network and credentials.")