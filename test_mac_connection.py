#!/usr/bin/env python3

import subprocess
import os
import time
import urllib.parse

def test_mac_connection():
    """Test connection to Mac shares at 192.168.68.112"""
    
    server = "192.168.68.112"
    username = "<PERSON>"
    password = "jsthogn"
    
    # Test shares  
    test_shares = ["micha<PERSON><PERSON>hols", "Home Theater"]
    
    print("=== Mac SMB Connection Test ===")
    print(f"Server: {server}")
    print(f"Username: {username}")
    print(f"Password: {'*' * len(password)}")
    print()
    
    # Check network connectivity
    print("1. Testing network connectivity...")
    try:
        result = subprocess.run(['ping', '-c', '2', server], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Network connectivity OK")
        else:
            print("❌ Network connectivity failed")
            return
    except Exception as e:
        print(f"❌ Network test error: {e}")
        return
    
    # Check SMB service
    print("\n2. Testing SMB service...")
    try:
        result = subprocess.run(['smbclient', '-L', server, '-U', f'{username}%{password}'], 
                              capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            print("✅ SMB service responding")
            print("Available shares:")
            for line in result.stdout.split('\n'):
                if 'Disk' in line and not line.strip().startswith('IPC'):
                    share = line.split()[0]
                    print(f"  📁 {share}")
        else:
            print(f"❌ SMB service error: {result.stderr}")
            return
    except Exception as e:
        print(f"❌ SMB test error: {e}")
        return
    
    # Test mounting shares
    print("\n3. Testing share mounting...")
    for share_name in test_shares:
        print(f"\nTesting: {share_name}")
        
        # Check if already mounted
        gvfs_path = f"/run/user/{os.getuid()}/gvfs/smb-share:server={server},share={urllib.parse.quote(share_name)}"
        
        if os.path.exists(gvfs_path):
            print(f"✅ {share_name} already mounted at {gvfs_path}")
            try:
                files = os.listdir(gvfs_path)[:5]  # List first 5 items
                print(f"   Contents: {files}")
            except Exception as e:
                print(f"   ⚠️ Cannot read contents: {e}")
            continue
        
        # Try different mounting methods
        methods = [
            f"smb://{urllib.parse.quote(username)}:{password}@{server}/{urllib.parse.quote(share_name)}",
            f"smb://Michael%20Nichols:{password}@{server}/{urllib.parse.quote(share_name)}",
        ]
        
        success = False
        for i, url in enumerate(methods, 1):
            try:
                print(f"   Method {i}: {url}")
                result = subprocess.run(['gio', 'mount', url], 
                                      capture_output=True, text=True, timeout=20)
                
                time.sleep(2)  # Wait for mount to settle
                
                if os.path.exists(gvfs_path):
                    print(f"   ✅ Successfully mounted!")
                    success = True
                    break
                else:
                    print(f"   ❌ Mount failed: {result.stderr.strip()}")
                    
            except subprocess.TimeoutExpired:
                print(f"   ⏱️ Method {i} timed out")
            except Exception as e:
                print(f"   ❌ Method {i} error: {e}")
        
        if not success:
            print(f"   ❌ All methods failed for {share_name}")
    
    print("\n=== Summary ===")
    print("If no shares mounted automatically, try manual connection:")
    print("1. Open file manager (nautilus)")
    print("2. Press Ctrl+L")
    print(f"3. Type: smb://{server}")
    print("4. Enter credentials when prompted:")
    print(f"   Username: {username}")
    print(f"   Password: {password}")
    print("   Domain: WORKGROUP")
    print()
    print("After manual connection, mounted shares will appear in:")
    print(f"/run/user/{os.getuid()}/gvfs/")

if __name__ == "__main__":
    test_mac_connection()